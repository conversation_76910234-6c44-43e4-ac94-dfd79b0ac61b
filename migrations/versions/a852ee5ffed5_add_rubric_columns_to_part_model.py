"""Add rubric columns to Part model

Revision ID: a852ee5ffed5
Revises: 1d8fafb3ad43
Create Date: 2025-07-12 09:31:12.854476

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a852ee5ffed5'
down_revision = '1d8fafb3ad43'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('parts', schema=None) as batch_op:
        batch_op.add_column(sa.Column('content_rubric', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('scoring_rubric', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('rubrics_generated_at', sa.DateTime(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('parts', schema=None) as batch_op:
        batch_op.drop_column('rubrics_generated_at')
        batch_op.drop_column('scoring_rubric')
        batch_op.drop_column('content_rubric')

    # ### end Alembic commands ###
