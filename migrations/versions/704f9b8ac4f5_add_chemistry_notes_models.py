"""Add chemistry notes models

Revision ID: 704f9b8ac4f5
Revises: a852ee5ffed5
Create Date: 2025-07-12 13:55:24.247018

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '704f9b8ac4f5'
down_revision = 'a852ee5ffed5'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('chemistry_chapter',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=False),
    sa.Column('chapter_number', sa.String(length=10), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('is_active', sa.<PERSON>(), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('slug')
    )
    op.create_table('chapter_section',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('chapter_id', sa.Integer(), nullable=False),
    sa.Column('section_id', sa.String(length=100), nullable=False),
    sa.Column('title', sa.String(length=500), nullable=False),
    sa.Column('heading_level', sa.Integer(), nullable=False),
    sa.Column('content', sa.Text(), nullable=True),
    sa.Column('raw_content', sa.Text(), nullable=True),
    sa.Column('page_number', sa.Integer(), nullable=True),
    sa.Column('order_index', sa.Integer(), nullable=False),
    sa.Column('parent_section_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['chapter_id'], ['chemistry_chapter.id'], ),
    sa.ForeignKeyConstraint(['parent_section_id'], ['chapter_section.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('chapter_id', 'section_id', name='_chapter_section_uc')
    )
    op.create_table('section_content',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('section_id', sa.Integer(), nullable=False),
    sa.Column('content_type', sa.String(length=50), nullable=False),
    sa.Column('content_data', sa.Text(), nullable=False),
    sa.Column('search_text', sa.Text(), nullable=True),
    sa.Column('order_index', sa.Integer(), nullable=False),
    sa.Column('meta_data', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['section_id'], ['chapter_section.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('section_content')
    op.drop_table('chapter_section')
    op.drop_table('chemistry_chapter')
    # ### end Alembic commands ###
