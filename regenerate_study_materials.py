#!/usr/bin/env python3
"""
Regenerate study materials with proper URLs for all parts
"""

import sys
import os
import json
from app import create_app
from models import db, Part
from preprocess import PlatoPreprocess

def regenerate_study_materials():
    """Regenerate study materials with proper URLs"""
    
    app = create_app()
    
    with app.app_context():
        # Get all parts that have study materials but might have broken URLs
        parts = Part.query.filter(
            Part.study_materials.isnot(None),
            Part.content_rubric.isnot(None)
        ).all()
        
        print(f"Found {len(parts)} parts with existing study materials")
        
        # Check if any have broken URLs
        broken_parts = []
        for part in parts:
            try:
                materials = json.loads(part.study_materials)
                for req_id, req_data in materials.items():
                    for material in req_data.get('materials', []):
                        if material.get('url') == '#' or not material.get('url', '').startswith('/notes/'):
                            broken_parts.append(part)
                            break
                    if part in broken_parts:
                        break
            except:
                broken_parts.append(part)
        
        print(f"Found {len(broken_parts)} parts with broken or missing URLs")
        
        if not broken_parts:
            print("✅ All study materials have proper URLs!")
            return
        
        # Initialize the preprocessor
        generator = PlatoPreprocess()
        
        print(f"🔄 Regenerating study materials for {len(broken_parts)} parts...")
        
        success_count = 0
        for i, part in enumerate(broken_parts, 1):
            print(f"\n[{i}/{len(broken_parts)}] Processing Part {part.id}...")
            
            try:
                result = generator.process_study_materials_for_part(part)
                if result['success']:
                    success_count += 1
                    print(f"✅ Successfully regenerated study materials for Part {part.id}")
                    
                    # Show sample URLs
                    materials = result['study_materials']
                    for req_id, req_data in list(materials.items())[:1]:  # Show first requirement
                        for material in req_data.get('materials', [])[:1]:  # Show first material
                            print(f"   📖 Sample URL: {material.get('url', 'N/A')}")
                            break
                        break
                else:
                    print(f"❌ Failed to regenerate for Part {part.id}: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                print(f"❌ Exception processing Part {part.id}: {str(e)}")
        
        print(f"\n🎉 Regeneration complete!")
        print(f"✅ Successfully processed: {success_count}/{len(broken_parts)} parts")
        print(f"❌ Failed: {len(broken_parts) - success_count}/{len(broken_parts)} parts")
        
        # Test a sample part
        if success_count > 0:
            print(f"\n🧪 Testing sample study materials...")
            test_part = Part.query.filter(Part.study_materials.isnot(None)).first()
            if test_part:
                materials = json.loads(test_part.study_materials)
                print(f"📋 Part {test_part.id} study materials:")
                
                for req_id, req_data in list(materials.items())[:2]:  # Show first 2 requirements
                    print(f"   Requirement {req_id}: {req_data['requirement'][:50]}...")
                    for material in req_data.get('materials', [])[:2]:  # Show first 2 materials
                        url = material.get('url', 'N/A')
                        title = material.get('title', 'N/A')
                        chapter = material.get('chapter', 'N/A')
                        print(f"     📖 {title} ({chapter})")
                        print(f"     🔗 URL: {url}")
                        
                        # Validate URL format
                        if url.startswith('/notes/') and '#' in url:
                            print(f"     ✅ URL format is correct")
                        else:
                            print(f"     ❌ URL format may be incorrect")
                        print()

if __name__ == "__main__":
    regenerate_study_materials()
