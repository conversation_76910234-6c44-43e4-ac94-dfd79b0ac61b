#!/usr/bin/env python3
"""
Upsert existing database content to Pinecone with hierarchical namespace
"""

import os
import sys
import re
from typing import List, Dict, Optional
from dataclasses import dataclass
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, ChemistryChapter, ChapterSection
from config import Config
from pinecone import Pinecone

NAMESPACE = "hierarchical"

# Initialize Pinecone
pc = Pinecone(api_key=os.getenv("PINECONE_API_KEY"))
index = pc.Index(host=os.getenv("PINECONE_INDEX_CHEM"))

@dataclass
class DatabaseContentSection:
    """Content section from database"""
    id: str
    heading_content: str
    anchor_id: str
    chapter: str
    chapter_slug: str
    heading_level: int
    page_number: Optional[int] = None

def extract_learning_outcomes_from_content(content: str) -> str:
    """Extract learning outcomes from section content if available"""
    if not content:
        return ""
    
    # Look for learning outcomes patterns in content
    content_lower = content.lower()
    
    # Common chemistry learning outcome keywords
    lo_keywords = [
        'understand', 'explain', 'describe', 'calculate', 'predict', 'compare',
        'identify', 'distinguish', 'apply', 'demonstrate', 'analyze', 'evaluate'
    ]
    
    # If content contains learning outcome language, use it
    if any(keyword in content_lower for keyword in lo_keywords):
        # Return first 200 characters as potential learning outcome
        return content[:200].strip()
    
    return ""

def get_content_sections_from_database() -> List[DatabaseContentSection]:
    """Extract content sections from existing database"""
    print("🔍 Extracting content sections from database...")
    
    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    content_sections = []
    
    try:
        # Get all chapters
        chapters = session.query(ChemistryChapter).filter_by(is_active=True).all()
        print(f"📚 Found {len(chapters)} chapters")
        
        for chapter in chapters:
            print(f"📖 Processing: {chapter.title}")
            
            # Get sections for this chapter
            sections = session.query(ChapterSection).filter_by(
                chapter_id=chapter.id
            ).filter(
                ChapterSection.hierarchical_anchor_id.isnot(None)
            ).order_by(ChapterSection.order_index).all()
            
            for section in sections:
                # Skip certain section types
                if should_skip_section(section.title):
                    continue
                
                # Create content section
                content_section = DatabaseContentSection(
                    id=f"{chapter.slug}-db-{section.id}",
                    heading_content=section.title,
                    anchor_id=section.hierarchical_anchor_id,
                    chapter=chapter.title,
                    chapter_slug=chapter.slug,
                    heading_level=section.heading_level,
                    page_number=section.page_number
                )
                
                content_sections.append(content_section)
        
        print(f"✅ Extracted {len(content_sections)} content sections")
        return content_sections
        
    except Exception as e:
        print(f"❌ Error extracting from database: {e}")
        return []
    
    finally:
        session.close()

def should_skip_section(title: str) -> bool:
    """Check if section should be skipped"""
    skip_patterns = [
        r'^Page\s+\d+',
        r'^RAFFLES INSTITUTION',
        r'^Year\s+\d+',
        r'^Lecture Notes',
        r'^\d+\.\d+\.\d+',  # Skip sub-sub-section numbers
        r'^Content$',
        r'^References?$',
        r'^Website$',
    ]
    
    for pattern in skip_patterns:
        if re.match(pattern, title, re.IGNORECASE):
            return True
    return False

def create_learning_outcome_mapping(sections: List[DatabaseContentSection]) -> Dict[str, str]:
    """Create a mapping of sections to learning outcomes based on content similarity"""
    print("🔗 Creating learning outcome mappings...")
    
    # Enhanced keyword mapping for chemistry topics
    topic_keywords = {
        'bonding': ['bond', 'ionic', 'covalent', 'metallic', 'intermolecular', 'hydrogen bonding'],
        'structure': ['structure', 'shape', 'geometry', 'vsepr', 'molecular', 'lattice'],
        'kinetics': ['rate', 'kinetic', 'catalyst', 'activation', 'mechanism', 'order'],
        'equilibrium': ['equilibrium', 'equilibria', 'constant', 'le chatelier', 'position'],
        'acids_bases': ['acid', 'base', 'ph', 'buffer', 'titration', 'indicator'],
        'organic': ['organic', 'alkane', 'alkene', 'alcohol', 'carbonyl', 'carboxylic', 'amine'],
        'energetics': ['energy', 'enthalpy', 'entropy', 'gibbs', 'thermodynamic', 'heat'],
        'electrochemistry': ['electrode', 'cell', 'potential', 'electrolysis', 'redox'],
        'periodic': ['periodic', 'group', 'period', 'trend', 'ionization', 'electronegativity']
    }
    
    # Generic learning outcomes for each topic
    learning_outcomes = {
        'bonding': 'understand and describe different types of chemical bonds including ionic, covalent, and metallic bonding',
        'structure': 'explain molecular shapes and structures using VSEPR theory and understand lattice structures',
        'kinetics': 'analyze reaction rates, mechanisms, and the effect of catalysts on chemical reactions',
        'equilibrium': 'apply equilibrium principles and Le Chatelier\'s principle to chemical systems',
        'acids_bases': 'understand acid-base theories, calculate pH, and explain buffer systems',
        'organic': 'describe organic compound structures, reactions, and functional group properties',
        'energetics': 'calculate and explain energy changes in chemical reactions and thermodynamic principles',
        'electrochemistry': 'understand electrochemical cells, electrode potentials, and electrolysis',
        'periodic': 'explain periodic trends and properties of elements across the periodic table'
    }
    
    section_mappings = {}
    
    for section in sections:
        heading_lower = section.heading_content.lower()
        
        # Find best matching topic
        best_topic = None
        best_score = 0
        
        for topic, keywords in topic_keywords.items():
            score = sum(1 for keyword in keywords if keyword in heading_lower)
            if score > best_score:
                best_score = score
                best_topic = topic
        
        # Assign learning outcome
        if best_topic and best_score > 0:
            section_mappings[section.id] = learning_outcomes[best_topic]
        else:
            # Default learning outcome
            section_mappings[section.id] = f"understand and apply concepts related to {section.heading_content.lower()}"
    
    return section_mappings

def upsert_to_pinecone(content_sections: List[DatabaseContentSection], batch_size: int = 50):
    """Upsert content sections to Pinecone with hierarchical namespace using upsert_records"""
    print(f"🚀 Upserting {len(content_sections)} content sections to Pinecone...")

    # Create learning outcome mappings
    lo_mappings = create_learning_outcome_mapping(content_sections)

    # Process in batches
    for i in range(0, len(content_sections), batch_size):
        batch = content_sections[i:i + batch_size]
        records = []

        for section in batch:
            # Get learning outcome for this section
            learning_outcome = lo_mappings.get(section.id, "")

            # Create text for embedding (combine heading and learning outcome)
            text_content = f"{section.heading_content}. {learning_outcome}"

            # Create record in the format expected by upsert_records
            record = {
                "_id": section.id,
                "content": text_content,  # Use 'content' field as expected by the index
                "heading_content": section.heading_content,
                "anchor_id": section.anchor_id,
                "learning_outcome": learning_outcome,
                "chapter": section.chapter,
                "chapter_slug": section.chapter_slug,
                "heading_level": section.heading_level,
                "page_number": section.page_number or 0,
                "url": f"/notes/{section.chapter_slug}#{section.anchor_id}",
                "document_name": section.chapter,
                "full_reference": f"{section.chapter} - {section.heading_content}"
            }

            records.append(record)

        if records:
            try:
                index.upsert_records(NAMESPACE, records)
                print(f"✅ Upserted batch {i//batch_size + 1}: {len(records)} records")
            except Exception as e:
                print(f"❌ Error upserting batch {i//batch_size + 1}: {e}")

    print(f"🎉 Upsert complete!")

def main():
    """Main function"""
    print("🧪 Chemistry Notes Database to Pinecone Upsert")
    print("=" * 50)
    
    # Extract content from database
    content_sections = get_content_sections_from_database()
    
    if not content_sections:
        print("❌ No content sections found")
        return
    
    print(f"\n📊 Summary:")
    print(f"   Content Sections: {len(content_sections)}")
    
    # Show breakdown by chapter
    chapter_stats = {}
    for section in content_sections:
        chapter = section.chapter
        if chapter not in chapter_stats:
            chapter_stats[chapter] = 0
        chapter_stats[chapter] += 1
    
    print(f"\n📚 Sections by Chapter:")
    for chapter, count in sorted(chapter_stats.items())[:5]:  # Show first 5
        print(f"   {chapter}: {count} sections")
    if len(chapter_stats) > 5:
        print(f"   ... and {len(chapter_stats) - 5} more chapters")
    
    # Upsert to Pinecone
    upsert_to_pinecone(content_sections)

if __name__ == "__main__":
    main()
