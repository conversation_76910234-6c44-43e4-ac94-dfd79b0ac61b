["test_intelligent_review.py::test_performance_analysis", "test_intelligent_review.py::test_recommendations", "test_intelligent_review.py::test_review_schedules", "test_intelligent_review.py::test_spaced_repetition", "test_streaming.py::test_prompt_formatting", "test_streaming.py::test_streaming_endpoint", "test_teacher_dashboard.py::test_files_exist", "test_teacher_dashboard.py::test_navigation_links", "test_teacher_dashboard.py::test_teacher_routes_syntax", "test_teacher_dashboard.py::test_template_exists", "test_title_generation.py::test_api_endpoints", "test_title_generation.py::test_title_generation", "tests/test_intelligent_review.py::test_performance_analysis", "tests/test_intelligent_review.py::test_recommendations", "tests/test_intelligent_review.py::test_review_schedules", "tests/test_intelligent_review.py::test_spaced_repetition", "tests/test_login.py::test_email_validation", "tests/test_login.py::test_email_validation_edge_cases", "tests/test_login.py::test_email_validation_with_client", "tests/test_streaming.py::test_prompt_formatting", "tests/test_streaming.py::test_streaming_endpoint", "tests/test_teacher_dashboard.py::test_files_exist", "tests/test_teacher_dashboard.py::test_navigation_links", "tests/test_teacher_dashboard.py::test_teacher_routes_syntax", "tests/test_teacher_dashboard.py::test_template_exists", "tests/test_title_generation.py::test_api_endpoints", "tests/test_title_generation.py::test_title_generation"]