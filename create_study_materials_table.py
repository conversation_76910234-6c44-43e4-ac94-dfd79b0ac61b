#!/usr/bin/env python3
"""
Create the study_materials database table for storing precomputed Pinecone results
"""

import sys
import os
from app import create_app
from models import db

def create_study_materials_table():
    """Create the study_materials table"""
    
    app = create_app()
    
    with app.app_context():
        # Create the study_materials table
        with db.engine.connect() as conn:
            conn.execute(db.text("""
                CREATE TABLE IF NOT EXISTS study_materials (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    part_id INTEGER NOT NULL,
                    requirement_id TEXT NOT NULL,
                    requirement_text TEXT NOT NULL,
                    part_description TEXT,
                    pinecone_payload TEXT NOT NULL,  -- JSON payload from Pinecone
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (part_id) REFERENCES parts (id),
                    UNIQUE(part_id, requirement_id)
                )
            """))

            # Create index for faster lookups
            conn.execute(db.text("""
                CREATE INDEX IF NOT EXISTS idx_study_materials_part_req
                ON study_materials (part_id, requirement_id)
            """))

            conn.commit()

            print("✅ Created study_materials table successfully")

            # Show table structure
            result = conn.execute(db.text("PRAGMA table_info(study_materials)"))
            columns = result.fetchall()
        
        print("\n📋 Table structure:")
        for col in columns:
            print(f"   {col[1]} {col[2]} {'NOT NULL' if col[3] else ''} {'PRIMARY KEY' if col[5] else ''}")

if __name__ == "__main__":
    create_study_materials_table()
