#!/usr/bin/env python3
"""
Chemistry Notes Preprocessing Script

This script preprocesses all chemistry notes content using Gemini LLM to:
1. Convert markdown to HTML
2. Clean up table remnants and artifacts
3. Process LaTeX equations
4. Fix image paths
5. Save cleaned HTML content to the database

Run this script BEFORE starting the Flask application.
"""

import os
import sys
import re
import json
import markdown
import google.generativeai as genai
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import time

# Add the current directory to Python path to import models
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, ChemistryChapter, ChapterSection, SectionContent
from config import Config

def setup_gemini():
    """Setup Gemini API"""
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("ERROR: GEMINI_API_KEY environment variable not set!")
        sys.exit(1)

    genai.configure(api_key=api_key)
    return genai.GenerativeModel('gemini-2.5-flash')

def process_content_block_parallel(args):
    """Process a single content block - designed for parallel execution"""
    block_id, content_data, content_type, thread_id = args

    try:
        print(f"    🔄 Thread {thread_id}: Processing block {block_id} (type: {content_type})")

        # Setup Gemini for this thread
        model = setup_gemini()

        # If it's already HTML, just clean it aggressively with Gemini
        # If it's text, convert to HTML first then clean
        if content_type == 'html':
            html_content = content_data  # Already HTML, just clean it
        else:
            html_content = process_markdown_to_html(content_data)  # Convert markdown to HTML first

        # Clean with Gemini (single block) using aggressive prompt
        cleaned_content = clean_content_with_gemini(model, [html_content])

        if cleaned_content and len(cleaned_content) > 0:
            result = cleaned_content[0]
        else:
            result = html_content

        print(f"    ✅ Thread {thread_id}: Block {block_id} completed")
        return block_id, result, True

    except Exception as e:
        print(f"    ❌ Thread {thread_id}: Block {block_id} failed: {e}")
        return block_id, content_data, False

def clean_content_with_gemini(model, content_batch):
    """Use Gemini to clean up a batch of content and remove table remnants"""
    try:
        # Combine multiple content blocks for batch processing
        combined_content = "\n\n<!-- CONTENT_SEPARATOR -->\n\n".join(content_batch)

        prompt = f"""
You are an expert content cleaner for educational chemistry notes. Your task is to AGGRESSIVELY clean up this HTML content by removing ALL markdown table remnants and artifacts while preserving properly formatted HTML tables.

The content contains multiple sections separated by "<!-- CONTENT_SEPARATOR -->". Process each section and return them in the same order, separated by the same separator.

CRITICAL INSTRUCTIONS - BE EXTREMELY AGGRESSIVE:
1. KEEP ALL properly formatted HTML tables (those with <table>, <tr>, <td>, <th> tags) - DO NOT REMOVE THESE
2. COMPLETELY REMOVE ALL markdown table remnants including:
   - Any text that looks like: "| text | text |"
   - Table separators like: "|---|---|" or "|:---:|:---:|"
   - Broken table fragments like: "| oxidation number | except in peroxides"
   - ANY line containing multiple | characters that isn't a proper HTML table
   - Table headers that got separated from their tables
   - Original markdown table code that was used to generate HTML tables
   - Leftover markdown table syntax after HTML table conversion
3. REMOVE image references like "[img-1.jpeg]", "[img-2.png]", etc.
4. REMOVE any unprocessed markdown links like "[text](url)"
5. PRESERVE all LaTeX content (anything with $ symbols) - DO NOT MODIFY THESE
6. PRESERVE all other HTML formatting and content
7. Clean up extra whitespace and empty paragraphs
8. If you see ANY text with | characters that looks like table remnants, DELETE IT COMPLETELY
9. After HTML tables have been inserted, remove any remaining original markdown table code
10. Return ONLY the cleaned HTML content sections separated by "<!-- CONTENT_SEPARATOR -->", no explanations or markdown code blocks

BE RUTHLESS - Remove ANY text that contains | characters unless it's inside a proper HTML table! This includes removing the original markdown table syntax that was converted to HTML tables.

Content to clean:
{combined_content}
"""

        response = model.generate_content(
            prompt,
            generation_config={
                'temperature': 0.1,  # Very deterministic
                'top_p': 0.95,
                'top_k': 40,
            }
        )

        cleaned_content = response.text.strip()

        # Remove any markdown code block wrappers if Gemini added them
        if cleaned_content.startswith('```html'):
            cleaned_content = cleaned_content[7:]
        if cleaned_content.endswith('```'):
            cleaned_content = cleaned_content[:-3]

        # Split back into individual content blocks
        cleaned_blocks = cleaned_content.split("<!-- CONTENT_SEPARATOR -->")
        cleaned_blocks = [block.strip() for block in cleaned_blocks]

        # Ensure we have the same number of blocks
        if len(cleaned_blocks) != len(content_batch):
            print(f"Warning: Expected {len(content_batch)} blocks, got {len(cleaned_blocks)}. Using original content.")
            return content_batch

        return cleaned_blocks

    except Exception as e:
        print(f"Gemini cleaning failed: {e}")
        return content_batch  # Return original content if Gemini fails

def nuclear_table_cleanup(html_content):
    """
    NUCLEAR OPTION: Remove ALL possible markdown table remnants using comprehensive regex
    """
    if not html_content:
        return html_content

    # Comprehensive list of markdown table patterns to remove
    patterns_to_remove = [
        # Table separator lines
        r'<p[^>]*>\s*\|[\s:|-]+\|\s*</p>',
        r'\|[\s:|-]+\|',

        # Lines with multiple pipes (markdown table rows)
        r'<p[^>]*>[^<]*\|[^<]*\|[^<]*</p>',

        # Any paragraph containing pipes
        r'<p[^>]*>[^<]*\|[^<]*</p>',

        # Standalone pipe characters in paragraphs
        r'<p[^>]*>\s*\|\s*</p>',

        # Table-like content patterns
        r'<p[^>]*>\s*\|[^|]*\|[^|]*\|\s*</p>',
        r'<p[^>]*>\s*\|[^|]*\|[^|]*\|[^|]*\|\s*</p>',
        r'<p[^>]*>\s*\|[^|]*\|[^|]*\|[^|]*\|[^|]*\|\s*</p>',

        # Empty paragraphs
        r'<p[^>]*>\s*</p>',
    ]

    # Apply all patterns
    for pattern in patterns_to_remove:
        html_content = re.sub(pattern, '', html_content, flags=re.MULTILINE | re.DOTALL)

    # Clean up whitespace
    html_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', html_content)

    return html_content

def aggressive_table_cleanup(html_content):
    """
    SUPER AGGRESSIVE cleanup to remove ALL markdown table remnants
    """
    if not html_content:
        return html_content

    # Step 1: Remove obvious markdown table patterns
    # Remove table separator lines like |---|---|
    html_content = re.sub(r'<p>\s*\|[\s:|-]+\|\s*</p>', '', html_content)
    html_content = re.sub(r'\|[\s:|-]+\|', '', html_content)

    # Step 2: Split into lines and process each line
    lines = html_content.split('\n')
    cleaned_lines = []
    inside_html_table = False

    for line in lines:
        stripped_line = line.strip()

        # Track HTML table boundaries
        if '<table' in line.lower() or '<div class="table-wrapper">' in line:
            inside_html_table = True
            cleaned_lines.append(line)
            continue
        elif '</table>' in line.lower() or '</div>' in line and inside_html_table:
            inside_html_table = False
            cleaned_lines.append(line)
            continue

        # If inside HTML table, keep everything
        if inside_html_table:
            cleaned_lines.append(line)
            continue

        # Outside HTML tables: AGGRESSIVE removal of markdown table remnants

        # Skip lines that look like markdown tables (contain multiple |)
        if stripped_line.count('|') >= 2:
            # Double check it's not HTML table content
            if not any(tag in stripped_line.lower() for tag in ['<tr', '<td', '<th', 'table']):
                continue  # Skip this line

        # Skip paragraph tags containing table-like content
        if re.match(r'<p[^>]*>\s*\|.*\|\s*</p>', stripped_line):
            continue

        # Skip lines that are just table separators
        if re.match(r'^[\s\|:-]+$', stripped_line):
            continue

        # Skip empty paragraphs
        if re.match(r'<p[^>]*>\s*</p>', stripped_line):
            continue

        cleaned_lines.append(line)

    # Rejoin
    html_content = '\n'.join(cleaned_lines)

    # Step 3: Final regex cleanup for any remaining artifacts
    # Remove any remaining | characters that are not in HTML tables
    html_content = re.sub(r'<p[^>]*>[^<]*\|[^<]*</p>', '', html_content)

    # Clean up excessive whitespace
    html_content = re.sub(r'\n\s*\n\s*\n+', '\n\n', html_content)
    html_content = re.sub(r'<p>\s*</p>', '', html_content)

    return html_content

def process_markdown_to_html(content):
    """Convert markdown to HTML with table and LaTeX support"""
    if not content:
        return ""
    
    # Step 1: Extract tables for separate processing
    table_blocks = []
    table_counter = 0
    
    def extract_table(match):
        nonlocal table_counter
        table_content = match.group(0)
        placeholder = f"<!--TABLE_PLACEHOLDER_{table_counter}-->"
        table_blocks.append((placeholder, table_content))
        table_counter += 1
        return placeholder
    
    # Extract tables
    table_pattern = r'(\|[^\n]+\|\n\|[\s:|-]+\|\n(?:\|[^\n]+\|\n?)*)'
    content = re.sub(table_pattern, extract_table, content, flags=re.MULTILINE)
    
    # Step 2: Protect LaTeX expressions
    latex_placeholders = []
    latex_counter = 0
    
    def protect_latex(match):
        nonlocal latex_counter
        latex_content = match.group(0)
        placeholder = f"LATEX_PLACEHOLDER_{latex_counter}"
        latex_placeholders.append((placeholder, latex_content))
        latex_counter += 1
        return placeholder
    
    # Protect LaTeX
    content = re.sub(r'\$\$([^$]+?)\$\$', protect_latex, content)
    content = re.sub(r'\$([^$\n]+?)\$', protect_latex, content)
    content = re.sub(r'\\\\?\(([^)]+?)\\\\?\)', protect_latex, content)
    content = re.sub(r'\\\\?\[([^\]]+?)\\\\?\]', protect_latex, content)
    
    # Step 3: Convert markdown to HTML
    md = markdown.Markdown(
        extensions=['tables', 'fenced_code', 'nl2br', 'attr_list'],
        extension_configs={'tables': {'use_align_attribute': True}}
    )
    html = md.convert(str(content))
    
    # Step 4: Process tables separately
    for table_placeholder, table_content in table_blocks:
        # Protect LaTeX in table
        table_latex_placeholders = []
        table_latex_counter = 0
        
        def protect_table_latex(match):
            nonlocal table_latex_counter
            latex_content = match.group(0)
            placeholder = f"TABLE_LATEX_{table_latex_counter}"
            table_latex_placeholders.append((placeholder, latex_content))
            table_latex_counter += 1
            return placeholder
        
        table_content = re.sub(r'\$\$([^$]+?)\$\$', protect_table_latex, table_content)
        table_content = re.sub(r'\$([^$\n|]+?)\$', protect_table_latex, table_content)
        
        # Convert table to HTML
        table_md = markdown.Markdown(extensions=['tables'])
        table_html = table_md.convert(table_content)
        
        # Restore LaTeX in table
        for latex_placeholder, latex_content in table_latex_placeholders:
            table_html = table_html.replace(latex_placeholder, latex_content)
        
        # Add CSS classes
        table_html = table_html.replace('<table>', '<div class="table-wrapper"><table class="table-auto w-full border-collapse border border-gray-300 my-6 bg-white">')
        table_html = table_html.replace('</table>', '</table></div>')
        table_html = table_html.replace('<th>', '<th class="border border-gray-300 px-4 py-3 bg-gray-50 font-semibold text-center text-sm">')
        table_html = table_html.replace('<td>', '<td class="border border-gray-300 px-4 py-3 text-center text-sm">')
        
        # Replace placeholder
        html = html.replace(table_placeholder, table_html)

    # Step 5: Remove any remaining markdown table remnants after HTML table insertion
    html = aggressive_table_cleanup(html)

    # Step 6: Restore LaTeX
    for placeholder, latex_content in latex_placeholders:
        html = html.replace(placeholder, latex_content)
    
    # Step 7: Fix image paths
    html = re.sub(r'<img alt="([^"]*)" src="images/([^"]*)"[^>]*/?>',
                  r'<img src="/chemistry-notes-images/\2" alt="\1" class="max-w-full h-auto rounded-lg shadow-sm mx-auto block my-4">',
                  html)

    # Step 8: Final nuclear cleanup - remove ALL possible markdown table remnants
    html = nuclear_table_cleanup(html)

    return html

def main():
    """Main preprocessing function - Focus on Alkanes chapter with 32-core parallel processing"""
    print("🧪 Starting Chemistry Notes Preprocessing (Alkanes Chapter) - 32 Core Parallel Processing...")

    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # Get only the alkanes chapter
        alkanes_chapter = session.query(ChemistryChapter).filter_by(slug='alkanes').first()
        if not alkanes_chapter:
            print("❌ Alkanes chapter not found!")
            sys.exit(1)

        print(f"📖 Processing chapter: {alkanes_chapter.title}")

        # Get all sections for alkanes chapter
        sections = session.query(ChapterSection).filter_by(chapter_id=alkanes_chapter.id).all()
        print(f"📄 Found {len(sections)} sections in alkanes chapter")

        # Collect all text blocks from all sections
        all_text_blocks = []
        block_to_section_map = {}

        for section in sections:
            content_blocks = session.query(SectionContent).filter_by(section_id=section.id).all()
            # Process both 'text' and 'html' blocks (html blocks need re-cleaning with aggressive prompt)
            processable_blocks = [block for block in content_blocks if
                                (block.content_type in ['text', 'html']) and block.content_data]

            for block in processable_blocks:
                all_text_blocks.append(block)
                block_to_section_map[block.id] = section.title

        if not all_text_blocks:
            print("❌ No text blocks found to process!")
            return

        print(f"� Found {len(all_text_blocks)} total text blocks across all sections")
        print(f"⚡ Starting parallel processing with 32 cores...")

        # Prepare arguments for parallel processing
        process_args = []
        for i, block in enumerate(all_text_blocks):
            process_args.append((block.id, block.content_data, block.content_type, i % 32))  # Include content type and distribute across 32 threads

        # Process all blocks in parallel using ThreadPoolExecutor
        results = {}
        completed_count = 0

        with ThreadPoolExecutor(max_workers=32) as executor:
            # Submit all tasks
            future_to_block = {
                executor.submit(process_content_block_parallel, args): args[0]
                for args in process_args
            }

            # Collect results as they complete
            for future in as_completed(future_to_block):
                block_id = future_to_block[future]
                try:
                    block_id, cleaned_content, success = future.result()
                    results[block_id] = (cleaned_content, success)
                    completed_count += 1

                    if completed_count % 10 == 0:  # Progress update every 10 blocks
                        print(f"    📊 Progress: {completed_count}/{len(all_text_blocks)} blocks completed")

                except Exception as e:
                    print(f"    ❌ Block {block_id} failed with exception: {e}")
                    results[block_id] = (None, False)

        print(f"🎉 Parallel processing completed! Updating database...")

        # Update all blocks with results
        successful_updates = 0
        failed_updates = 0

        for block in all_text_blocks:
            if block.id in results:
                cleaned_content, success = results[block.id]
                if success and cleaned_content:
                    block.content_data = cleaned_content
                    block.content_type = 'html'  # Mark as preprocessed HTML
                    successful_updates += 1
                else:
                    failed_updates += 1
                    print(f"    ⚠️  Block {block.id} in section '{block_to_section_map.get(block.id, 'Unknown')}' failed to process")

        # Commit all changes
        session.commit()

        print(f"\n🎉 Alkanes chapter preprocessing completed!")
        print(f"📊 Results:")
        print(f"   ✅ Successfully processed: {successful_updates} blocks")
        print(f"   ❌ Failed to process: {failed_updates} blocks")
        print(f"   📈 Success rate: {(successful_updates/(successful_updates+failed_updates)*100):.1f}%")
        print(f"💾 All cleaned content saved to database")
        print(f"🚀 Ready to restart Flask application!")

    except Exception as e:
        session.rollback()
        print(f"\n❌ Error during preprocessing: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

    finally:
        session.close()

if __name__ == "__main__":
    main()
