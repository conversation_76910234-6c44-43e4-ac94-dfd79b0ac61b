#!/usr/bin/env python3
"""
Verify that the database integration is working correctly
"""

import sys
import os
import json
from app import create_app
from models import db, Part
import logging

def verify_database_integration():
    """Verify the database integration works correctly"""
    
    app = create_app()
    
    with app.app_context():
        # Set up logging
        logger = logging.getLogger('test')
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
        logger.addHandler(handler)
        
        print("🔍 Verifying Database Integration...")
        
        # Check database records
        with db.engine.connect() as conn:
            result = conn.execute(db.text('SELECT COUNT(*) FROM study_materials'))
            total_records = result.fetchone()[0]
            print(f"📊 Total study materials records: {total_records}")
            
            if total_records == 0:
                print("❌ No study materials found in database")
                return False
            
            # Get a sample part with study materials
            result = conn.execute(db.text("""
                SELECT DISTINCT part_id FROM study_materials LIMIT 1
            """))
            part_id = result.fetchone()[0]
            
            # Get the part
            part = Part.query.get(part_id)
            if not part:
                print(f"❌ Part {part_id} not found")
                return False
            
            print(f"🧪 Testing with Part {part.id}")
            
            # Parse content rubric
            try:
                content_rubric = json.loads(part.content_rubric)
                print(f"📋 Content rubric has {len(content_rubric)} requirements")
            except Exception as e:
                print(f"❌ Error parsing content rubric: {str(e)}")
                return False
            
            # Test the database fetch directly
            print("🔧 Testing database fetch directly...")

            try:
                # Fetch study materials directly from database
                result = conn.execute(db.text("""
                    SELECT requirement_id, pinecone_payload
                    FROM study_materials
                    WHERE part_id = :part_id
                """), {'part_id': part.id})

                rows = result.fetchall()

                study_materials = {}
                for row in rows:
                    req_id = row[0]
                    payload_json = row[1]

                    try:
                        # Parse the Pinecone payload
                        pinecone_results = json.loads(payload_json)

                        # Format the materials for the requirement
                        study_materials[req_id] = {
                            'requirement': content_rubric.get(req_id, ''),
                            'materials': pinecone_results[:3]  # Top 3 results
                        }

                    except json.JSONDecodeError as e:
                        print(f"⚠️  Failed to parse payload for req {req_id}: {str(e)}")
                        continue
                
                if not study_materials:
                    print("❌ No study materials returned from function")
                    return False
                
                print(f"✅ Function returned study materials for {len(study_materials)} requirements")
                
                # Verify structure
                for req_id, materials_data in study_materials.items():
                    if 'requirement' not in materials_data or 'materials' not in materials_data:
                        print(f"❌ Invalid structure for requirement {req_id}")
                        return False
                    
                    materials = materials_data['materials']
                    print(f"   📚 Requirement {req_id}: {len(materials)} study materials")
                    
                    # Check first material structure
                    if materials:
                        first_material = materials[0]
                        required_fields = ['title', 'url']
                        for field in required_fields:
                            if field not in first_material:
                                print(f"❌ Missing field '{field}' in study material")
                                return False
                        
                        print(f"      📖 Sample: {first_material['title']}")
                        print(f"      🔗 URL: {first_material['url']}")
                
                print("✅ All study materials have correct structure")
                
                # Test URL extraction
                urls_found = 0
                placeholder_urls = 0
                
                for req_id, materials_data in study_materials.items():
                    for material in materials_data['materials']:
                        url = material.get('url', '')
                        if url:
                            urls_found += 1
                            if url == '#':
                                placeholder_urls += 1
                
                print(f"🔗 URL Analysis:")
                print(f"   Total URLs: {urls_found}")
                print(f"   Placeholder URLs (#): {placeholder_urls}")
                print(f"   Working URLs: {urls_found - placeholder_urls}")
                
                if urls_found > 0:
                    print("✅ URLs are being extracted from Pinecone payload")
                else:
                    print("⚠️  No URLs found in study materials")
                
                # Test performance
                import time
                start_time = time.time()

                # Run the database query multiple times to test performance
                for i in range(5):
                    result = conn.execute(db.text("""
                        SELECT requirement_id, pinecone_payload
                        FROM study_materials
                        WHERE part_id = :part_id
                    """), {'part_id': part.id})
                    rows = result.fetchall()

                end_time = time.time()
                avg_time = (end_time - start_time) / 5
                
                print(f"⚡ Performance Test:")
                print(f"   Average fetch time: {avg_time:.3f} seconds")
                
                if avg_time < 0.1:
                    print("✅ Excellent performance (< 0.1s)")
                elif avg_time < 0.5:
                    print("✅ Good performance (< 0.5s)")
                else:
                    print("⚠️  Slow performance (> 0.5s)")
                
                return True
                
            except Exception as e:
                print(f"❌ Error testing function: {str(e)}")
                import traceback
                traceback.print_exc()
                return False

def main():
    """Main function"""
    print("🚀 Database Integration Verification")
    print("=" * 50)
    
    success = verify_database_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 DATABASE INTEGRATION VERIFICATION PASSED!")
        print("✅ The new study materials system is working correctly")
        print("✅ Database lookups are fast and reliable")
        print("✅ Study materials structure is valid")
        print("✅ Ready for production use")
    else:
        print("❌ DATABASE INTEGRATION VERIFICATION FAILED!")
        print("❌ Please check the issues above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
