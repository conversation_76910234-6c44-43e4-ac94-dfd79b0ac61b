#!/usr/bin/env python3
"""
Database migration script to add study materials columns to the parts table
"""

import sqlite3
import os
from app import create_app

def migrate_database():
    """Add study materials columns to the parts table"""
    
    app = create_app()
    
    with app.app_context():
        # Get database path from app config
        database_url = app.config.get('SQLALCHEMY_DATABASE_URI', 'sqlite:///database.db')
        
        # Extract path from SQLite URL
        if database_url.startswith('sqlite:///'):
            db_path = database_url[10:]  # Remove 'sqlite:///'
        else:
            print("❌ This migration script only supports SQLite databases")
            return False
        
        if not os.path.exists(db_path):
            print(f"❌ Database file not found: {db_path}")
            return False
        
        print(f"📁 Database path: {db_path}")
        
        try:
            # Connect to database
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Check if columns already exist
            cursor.execute("PRAGMA table_info(parts)")
            columns = [column[1] for column in cursor.fetchall()]
            
            migrations_needed = []
            
            if 'study_materials' not in columns:
                migrations_needed.append('study_materials')
            
            if 'study_materials_generated_at' not in columns:
                migrations_needed.append('study_materials_generated_at')
            
            if not migrations_needed:
                print("✅ All study materials columns already exist")
                return True
            
            print(f"🔧 Adding columns: {', '.join(migrations_needed)}")
            
            # Add study_materials column
            if 'study_materials' in migrations_needed:
                cursor.execute("""
                    ALTER TABLE parts 
                    ADD COLUMN study_materials TEXT
                """)
                print("✅ Added study_materials column")
            
            # Add study_materials_generated_at column
            if 'study_materials_generated_at' in migrations_needed:
                cursor.execute("""
                    ALTER TABLE parts 
                    ADD COLUMN study_materials_generated_at DATETIME
                """)
                print("✅ Added study_materials_generated_at column")
            
            # Commit changes
            conn.commit()
            
            # Verify the migration
            cursor.execute("PRAGMA table_info(parts)")
            columns_after = [column[1] for column in cursor.fetchall()]
            
            success = all(col in columns_after for col in ['study_materials', 'study_materials_generated_at'])
            
            if success:
                print("✅ Migration completed successfully")
                
                # Show statistics
                cursor.execute("SELECT COUNT(*) FROM parts WHERE input_type = 'saq'")
                saq_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM parts WHERE content_rubric IS NOT NULL")
                rubrics_count = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM parts WHERE study_materials IS NOT NULL")
                study_materials_count = cursor.fetchone()[0]
                
                print(f"\n📊 Database Statistics:")
                print(f"   SAQ Parts: {saq_count}")
                print(f"   Parts with Rubrics: {rubrics_count}")
                print(f"   Parts with Study Materials: {study_materials_count}")
                
                if rubrics_count > study_materials_count:
                    print(f"\n💡 You can precompute study materials for {rubrics_count - study_materials_count} parts")
                    print("   Run: python3 preprocess.py --study-materials-only")
                
            else:
                print("❌ Migration verification failed")
            
            conn.close()
            return success
            
        except Exception as e:
            print(f"❌ Migration failed: {str(e)}")
            try:
                conn.rollback()
                conn.close()
            except:
                pass
            return False

if __name__ == "__main__":
    print("🚀 Starting database migration for study materials...")
    success = migrate_database()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("\nNext steps:")
        print("1. Run: python3 preprocess.py --study-materials-only")
        print("2. This will precompute study materials for all parts with existing rubrics")
    else:
        print("\n💥 Migration failed!")
        exit(1)
