import os
from functools import wraps
from flask import session, redirect, url_for, flash, send_from_directory, request, jsonify, render_template # Import request and jsonify
from models import db, User, DailyActivity # Assuming models.py defines User and db
from datetime import datetime
import logging

# Get loggers (assuming they are configured in app.py)
app_logger = logging.getLogger('app')
user_logger = logging.getLogger('user_activity')
error_logger = logging.getLogger('errors')

# --- Constants ---
ALLOWED_EXTENSIONS = {'pdf'}
ALLOWED_DATA_EXTENSIONS = {'csv', 'xlsx', 'xls'}
ALLOWED_ATTACHMENTS = {'jpg', 'png', 'jpeg', 'pdf'}

# --- Helper Functions ---

def allowed_file(filename, allowed_extensions):
    """Checks if the file extension is allowed."""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def update_user_activity(user_id):
    """Updates the last_active timestamp for a user."""
    from sqlalchemy.exc import OperationalError
    import time

    # Skip if no user_id provided
    if not user_id:
        return

    # Use a separate session for user activity updates to avoid conflicts with main request
    from flask import current_app
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import create_engine

    # Get database URI from app config
    engine = create_engine(current_app.config['SQLALCHEMY_DATABASE_URI'])
    Session = sessionmaker(bind=engine)
    session = Session()

    max_retries = 3
    retry_delay = 0.1  # seconds

    for attempt in range(max_retries):
        try:
            # Get user in this session
            user = session.query(User).get(user_id)
            if not user:
                session.close()
                return

            # Update last_active timestamp
            user.last_active = datetime.now()
            session.commit()

            # Close this session
            session.close()

            # Increment daily activity in a separate operation
            increment_daily_activity(user_id)
            return

        except OperationalError as e:
            # Handle database locks specifically
            if "database is locked" in str(e) and attempt < max_retries - 1:
                session.rollback()
                time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                continue
            else:
                session.rollback()
                session.close()
                error_logger.error(f"Error updating activity for user {user_id} after {attempt+1} attempts: {str(e)}")
                return
        except Exception as e:
            session.rollback()
            session.close()
            error_logger.error(f"Error updating activity for user {user_id}: {str(e)}")
            return

def increment_daily_activity(user_id):
    """Increments the daily activity counter for a user.

    This ensures we track user engagement by date for the activity heatmap.
    """
    from sqlalchemy.exc import OperationalError
    import time

    # Skip if no user_id provided
    if not user_id:
        return

    # Use a separate session for activity tracking to avoid conflicts
    from flask import current_app
    from sqlalchemy.orm import sessionmaker
    from sqlalchemy import create_engine

    # Get database URI from app config
    engine = create_engine(current_app.config['SQLALCHEMY_DATABASE_URI'])
    Session = sessionmaker(bind=engine)
    session = Session()

    today = datetime.now().date()
    max_retries = 3
    retry_delay = 0.1  # seconds

    for attempt in range(max_retries):
        try:
            # Try to find an existing record for today
            daily_activity = session.query(DailyActivity).filter_by(
                user_id=user_id,
                date=today
            ).first()

            if daily_activity:
                # Increment existing record
                daily_activity.activity_count += 1
            else:
                # Create new record for today
                daily_activity = DailyActivity(
                    user_id=user_id,
                    date=today,
                    activity_count=1
                )
                session.add(daily_activity)

            session.commit()
            session.close()
            return

        except OperationalError as e:
            # Handle database locks specifically
            if "database is locked" in str(e) and attempt < max_retries - 1:
                session.rollback()
                time.sleep(retry_delay * (attempt + 1))  # Exponential backoff
                continue
            else:
                session.rollback()
                session.close()
                error_logger.error(f"Error updating daily activity for user {user_id} after {attempt+1} attempts: {str(e)}")
                return
        except Exception as e:
            session.rollback()
            session.close()
            error_logger.error(f"Error updating daily activity for user {user_id}: {str(e)}")
            return
        
def register_utility_routes(app, db, session):
    # Route to clear the confetti flag
    @app.route('/clear_confetti', methods=['POST'])
    def clear_confetti():
        if 'show_confetti' in session:
            session.pop('show_confetti')
        return jsonify({'status': 'success'})

    # API endpoint for checking username availability
    @app.route('/api/check_username', methods=['POST'])
    def check_username():
        """Check if a username is available"""
        data = request.get_json()
        if not data or 'username' not in data:
            return jsonify({'error': 'Username is required'}), 400

        username = data['username'].strip()

        # Basic validation
        if len(username) < 3:
            return jsonify({'available': False, 'reason': 'Username must be at least 3 characters long'})

        if not username.replace('_', '').replace('-', '').isalnum():
            return jsonify({'available': False, 'reason': 'Username can only contain letters, numbers, hyphens, and underscores'})

        # Check if username exists
        existing_user = User.query.filter_by(username=username).first()

        return jsonify({
            'available': existing_user is None,
            'username': username
        })

    # Debug route for OAuth issues (development only)
    @app.route('/debug/session')
    def debug_session():
        if os.getenv('FLASK_ENV') == 'production':
            return "Not available in production", 404

        session_data = dict(session)
        return jsonify({
            'session_data': session_data,
            'session_id': session.get('_id', 'No ID'),
            'permanent': session.permanent
        })

    # Time tracking test page (development only)
    @app.route('/test/time-tracking')
    @login_required
    def time_tracking_test():
        if os.getenv('FLASK_ENV') == 'production':
            return "Not available in production", 404

        return render_template('time_tracking_test.html')
        
# --- Decorators ---

def login_required(f):
    """Decorator to require login for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('login')) # Assuming login route is named 'auth.login' after refactor
        # Optionally, update activity on access to protected routes
        # update_user_activity(session['user_id'])
        return f(*args, **kwargs)
    return decorated_function

def admin_required(f):
    """Decorator to require admin privileges for a route."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('Please log in to access this page.', 'warning')
            return redirect(url_for('login'))

        user = User.query.get(session['user_id'])
        if not user:
            flash('User not found. Please log in again.', 'error')
            session.clear() # Clear potentially invalid session
            return redirect(url_for('login'))

        if user.role != 'admin':
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('index'))

        # Optionally, update activity for admins too
        # update_user_activity(session['user_id'])
        return f(*args, **kwargs)
    return decorated_function

# --- File Serving ---
# Moved from file_serve blueprint

def register_file_serving_routes(app):
    # File serving is now handled by the serve_bp blueprint in file_serve.py
    # This function is kept for backward compatibility but only registers the content route

    @app.route("/content/<topic>/<path:filename>")
    def serve_content_file(topic, filename):
        """Serve files from the content directory (e.g., PDFs for dojo)."""
        # Basic security check
        if '..' in filename or filename.startswith('/') or '..' in topic or topic.startswith('/'):
             return "Invalid path", 400

        content_base_path = os.path.join(os.getcwd(), 'content')
        # Ensure the resolved path is still within the content directory
        safe_topic_path = os.path.abspath(os.path.join(content_base_path, topic))
        safe_file_path = os.path.abspath(os.path.join(safe_topic_path, filename))

        if not safe_topic_path.startswith(content_base_path) or not safe_file_path.startswith(safe_topic_path):
            return "Access denied", 403

        if not os.path.exists(safe_topic_path) or not os.path.exists(safe_file_path):
             return "File not found", 404

        response = send_from_directory(safe_topic_path, filename)

        # Set correct content type for PDFs
        if filename.lower().endswith('.pdf'):
            response.headers['Content-Type'] = 'application/pdf'
            # Optional: prevent browser from displaying inline for download
            # response.headers['Content-Disposition'] = f'attachment; filename="{filename}"'

        return response

# --- Attachment Handling ---
# Moved from attachment_handler blueprint - This might need more context
# If it was just serving files, serve_upload above handles it.
# If it involved database interactions (e.g., linking attachments to questions),
# that logic should move to the relevant route module (e.g., admin.py or vault.py).
# For now, assuming it was primarily file serving.

# --- Template Filters ---
def register_template_filters(app):
    @app.template_filter('format_time')
    def format_time_filter(seconds):
        """Formats seconds into HH:MM:SS."""
        if not isinstance(seconds, (int, float)) or seconds < 0:
            return "00:00:00" # Or handle invalid input differently
        seconds = int(seconds)
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{hours:02}:{minutes:02}:{secs:02}"

    @app.template_filter('from_json')
    def from_json_filter(json_string):
        """Parse JSON string to Python object."""
        import json
        try:
            if json_string:
                return json.loads(json_string)
            return {}
        except (json.JSONDecodeError, TypeError):
            return {}

    @app.template_filter('markdown')
    def markdown_filter(text):
        """Process content - either preprocessed HTML or markdown."""
        import markdown
        import re
        from flask import url_for

        if not text:
            return ""

        # Check if content is already preprocessed HTML (contains HTML tags)
        if '<table>' in text or '<p>' in text or '<div>' in text:
            # Content is already preprocessed HTML, just fix image paths
            def replace_image_path(match):
                alt_text = match.group(1)
                image_path = match.group(2)
                if image_path.startswith('/chemistry-notes-images/'):
                    return match.group(0)  # Already processed
                elif image_path.startswith('images/'):
                    filename = image_path.replace('images/', '')
                    new_url = url_for('serve_chemistry_notes_image', filename=filename)
                    return f'<img src="{new_url}" alt="{alt_text}" class="max-w-full h-auto rounded-lg shadow-sm mx-auto block my-4">'
                return match.group(0)

            html = re.sub(r'<img[^>]*alt="([^"]*)"[^>]*src="([^"]*)"[^>]*/?>', replace_image_path, text)
            return html

        # Fallback: process as markdown (for non-preprocessed content)
        # Extract tables
        table_blocks = []
        table_counter = 0

        def extract_table(match):
            nonlocal table_counter
            table_content = match.group(0)
            placeholder = f"<!--TABLE_PLACEHOLDER_{table_counter}-->"
            table_blocks.append((placeholder, table_content))
            table_counter += 1
            return placeholder

        table_pattern = r'(\|[^\n]+\|\n\|[\s:|-]+\|\n(?:\|[^\n]+\|\n?)*)'
        text = re.sub(table_pattern, extract_table, text, flags=re.MULTILINE)

        # Protect LaTeX
        latex_placeholders = []
        placeholder_counter = 0

        def protect_latex(match):
            nonlocal placeholder_counter
            latex_content = match.group(0)
            placeholder = f"LATEX_PLACEHOLDER_{placeholder_counter}"
            latex_placeholders.append((placeholder, latex_content))
            placeholder_counter += 1
            return placeholder

        text = re.sub(r'\$\$([^$]+?)\$\$', protect_latex, text)
        text = re.sub(r'\$([^$\n]+?)\$', protect_latex, text)
        text = re.sub(r'\\\\?\(([^)]+?)\\\\?\)', protect_latex, text)
        text = re.sub(r'\\\\?\[([^\]]+?)\\\\?\]', protect_latex, text)

        # Convert markdown to HTML
        md = markdown.Markdown(
            extensions=['tables', 'fenced_code', 'nl2br', 'attr_list'],
            extension_configs={'tables': {'use_align_attribute': True}}
        )
        html = md.convert(str(text))

        # Process tables
        for table_placeholder, table_content in table_blocks:
            table_latex_placeholders = []
            table_latex_counter = 0

            def protect_table_latex(match):
                nonlocal table_latex_counter
                latex_content = match.group(0)
                placeholder = f"TABLE_LATEX_{table_latex_counter}"
                table_latex_placeholders.append((placeholder, latex_content))
                table_latex_counter += 1
                return placeholder

            table_content = re.sub(r'\$\$([^$]+?)\$\$', protect_table_latex, table_content)
            table_content = re.sub(r'\$([^$\n|]+?)\$', protect_table_latex, table_content)

            table_md = markdown.Markdown(extensions=['tables'])
            table_html = table_md.convert(table_content)

            for latex_placeholder, latex_content in table_latex_placeholders:
                table_html = table_html.replace(latex_placeholder, latex_content)

            table_html = table_html.replace('<table>', '<div class="table-wrapper"><table class="table-auto w-full border-collapse border border-gray-300 my-6 bg-white">')
            table_html = table_html.replace('</table>', '</table></div>')
            table_html = table_html.replace('<th>', '<th class="border border-gray-300 px-4 py-3 bg-gray-50 font-semibold text-center text-sm">')
            table_html = table_html.replace('<td>', '<td class="border border-gray-300 px-4 py-3 text-center text-sm">')

            html = html.replace(table_placeholder, table_html)

        # Restore LaTeX
        for placeholder, latex_content in latex_placeholders:
            html = html.replace(placeholder, latex_content)

        # Fix image paths
        def replace_image_path(match):
            alt_text = match.group(1)
            image_path = match.group(2)
            if image_path.startswith('images/'):
                filename = image_path.replace('images/', '')
                new_url = url_for('serve_chemistry_notes_image', filename=filename)
                return f'<img src="{new_url}" alt="{alt_text}" class="max-w-full h-auto rounded-lg shadow-sm mx-auto block my-4">'
            return match.group(0)

        html = re.sub(r'<img alt="([^"]*)" src="([^"]*)"[^>]*/?>', replace_image_path, html)

        return html

# --- Context Processors ---
def register_context_processors(app):
    @app.context_processor
    def utility_processor():
        """Injects variables/functions into all templates."""
        # Be selective about what's injected globally
        return {
            'User': User # Make User model available in templates if needed
            # Add other common variables/functions if necessary
        }

# --- Logging Hooks ---
def register_logging_hooks(app):
    # Make sessions permanent by default (moved from original app.py)
    @app.before_request
    def make_session_permanent():
        session.permanent = True

    # Track last activity update time to limit frequency
    from datetime import datetime, timedelta
    last_activity_updates = {}
    min_update_interval = timedelta(seconds=30)  # Only update activity every 30 seconds per user

    @app.before_request
    def log_request_info():
        # Avoid logging requests for static files and auto_save to reduce noise
        if request.path.startswith('/static') or request.path == '/auto_save':
            return

        # Log essential request details for non-static requests
        user_str = session.get('username', 'Anonymous')
        user_id_str = f" (ID: {session.get('user_id')})" if 'user_id' in session else ""
        app_logger.info(
            f"Request - Path: {request.path}, Method: {request.method}, "
            f"IP: {request.remote_addr}, User: {user_str}{user_id_str}"
        )

        # Update user activity with rate limiting to reduce database load
        if 'user_id' in session:
            user_id = session['user_id']
            now = datetime.now()

            # Check if we should update activity for this user
            last_update = last_activity_updates.get(user_id)
            if last_update is None or (now - last_update) > min_update_interval:
                # Update activity and record the time
                update_user_activity(user_id)
                last_activity_updates[user_id] = now

                # Clean up old entries in the dictionary to prevent memory leaks
                # Remove entries older than 1 hour
                cleanup_time = now - timedelta(hours=1)
                for uid in list(last_activity_updates.keys()):
                    if last_activity_updates[uid] < cleanup_time:
                        del last_activity_updates[uid]

    # Add after_request logging if needed (e.g., log response status)
    # @app.after_request
    # def log_response_info(response):
    #     if not request.path.startswith('/static'):
    #         app_logger.info(f"Response - Status: {response.status_code}")
    #     return response

# --- Error Handlers ---
def register_error_handlers(app, limiter): # Pass limiter if needed

    # Custom error handler for rate limit exceeded (returns JSON)
    @app.errorhandler(429)
    def ratelimit_handler(e):
        error_logger.warning(f"Rate limit exceeded for {request.remote_addr} (User: {session.get('username', 'Anonymous')}) - {e.description}")
        # Check if the request likely expects JSON (e.g., AJAX)
        if request.accept_mimetypes.accept_json and not request.accept_mimetypes.accept_html:
             return jsonify(
                 status="error",
                 message=f"Rate limit exceeded: {e.description}. Please wait a moment and try again."
             ), 429
        else:
            # Render an HTML error page for regular browser requests
            flash(f"Too many requests: {e.description}. Please wait a moment and try again.", "error")
            # Redirect to a sensible page, maybe the previous one or index
            return redirect(request.referrer or url_for('index')) # Assumes core.index exists

    # Add handlers for other common errors (404, 500) if desired
    @app.errorhandler(404)
    def not_found_error(error):
         error_logger.info(f"404 Not Found: {request.path} (User: {session.get('username', 'Anonymous')})")
         # Render a custom 404 page
         # return render_template('404.html'), 404
         return "Page Not Found", 404 # Simple response

    @app.errorhandler(500)
    def internal_error(error):
         # Log the full exception trace
         error_logger.exception(f"500 Internal Server Error: {request.path} (User: {session.get('username', 'Anonymous')})")
         # Important: Rollback the session in case of database errors during the request
         db.session.rollback()
         # Render a custom 500 page
         # return render_template('500.html'), 500
         return "An internal server error occurred.", 500 # Simple response
