"""
Chemistry Notes Routes
Handles serving chemistry notes with section tagging and search functionality.
"""

import os
import json
import markdown
from flask import render_template, request, jsonify, abort, url_for, redirect, send_from_directory
from sqlalchemy import or_, and_
from sqlalchemy.orm import joinedload

from models import db, ChemistryChapter, ChapterSection, SectionContent
from .utils import login_required, error_logger, app_logger


def register_chemistry_notes_routes(app, db, session):
    """Register chemistry notes routes"""
    
    @app.route("/notes")
    @login_required
    def notes_index():
        """Display all available chemistry chapters"""
        try:
            chapters = ChemistryChapter.query.filter_by(is_active=True).order_by(
                ChemistryChapter.chapter_number,
                ChemistryChapter.title
            ).all()
            
            return render_template("notes/index.html", chapters=chapters)
        except Exception as e:
            error_logger.error(f"Error loading notes index: {e}")
            return render_template("notes/index.html", chapters=[])
    
    @app.route("/notes/<chapter_slug>")
    @login_required
    def view_chapter(chapter_slug):
        """Display a specific chemistry chapter with all sections"""
        try:
            chapter = ChemistryChapter.query.filter_by(
                slug=chapter_slug, 
                is_active=True
            ).options(
                joinedload(ChemistryChapter.sections)
            ).first()
            
            if not chapter:
                abort(404)
            
            # Get sections ordered by their index
            sections = ChapterSection.query.filter_by(
                chapter_id=chapter.id
            ).order_by(ChapterSection.order_index).all()
            
            # Build section hierarchy for navigation
            section_hierarchy = build_section_hierarchy(sections)
            
            return render_template("notes/chapter.html", 
                                 chapter=chapter, 
                                 sections=sections,
                                 section_hierarchy=section_hierarchy)
        except Exception as e:
            error_logger.error(f"Error loading chapter {chapter_slug}: {e}")
            abort(500)
    
    @app.route("/notes/<chapter_slug>/<section_id>")
    @login_required
    def view_section(chapter_slug, section_id):
        """Display a specific section within a chapter"""
        try:
            chapter = ChemistryChapter.query.filter_by(
                slug=chapter_slug, 
                is_active=True
            ).first()
            
            if not chapter:
                abort(404)
            
            section = ChapterSection.query.filter_by(
                chapter_id=chapter.id,
                section_id=section_id
            ).options(
                joinedload(ChapterSection.content_blocks)
            ).first()
            
            if not section:
                abort(404)
            
            # Get neighboring sections for navigation
            prev_section = ChapterSection.query.filter(
                ChapterSection.chapter_id == chapter.id,
                ChapterSection.order_index < section.order_index
            ).order_by(ChapterSection.order_index.desc()).first()
            
            next_section = ChapterSection.query.filter(
                ChapterSection.chapter_id == chapter.id,
                ChapterSection.order_index > section.order_index
            ).order_by(ChapterSection.order_index.asc()).first()
            
            # Get all sections for sidebar navigation
            all_sections = ChapterSection.query.filter_by(
                chapter_id=chapter.id
            ).order_by(ChapterSection.order_index).all()
            
            section_hierarchy = build_section_hierarchy(all_sections)
            
            return render_template("notes/section.html",
                                 chapter=chapter,
                                 section=section,
                                 prev_section=prev_section,
                                 next_section=next_section,
                                 section_hierarchy=section_hierarchy)
        except Exception as e:
            error_logger.error(f"Error loading section {chapter_slug}/{section_id}: {e}")
            abort(500)
    
    @app.route("/api/notes/search")
    @login_required
    def search_notes():
        """Search through chemistry notes content"""
        try:
            query = request.args.get('q', '').strip()
            chapter_slug = request.args.get('chapter', '')
            limit = min(int(request.args.get('limit', 20)), 100)
            
            if not query:
                return jsonify({'results': [], 'total': 0})
            
            # Build search query
            search_conditions = []
            
            # Search in section titles
            search_conditions.append(ChapterSection.title.ilike(f'%{query}%'))
            
            # Search in content blocks
            content_search = db.session.query(SectionContent.section_id).filter(
                or_(
                    SectionContent.search_text.ilike(f'%{query}%'),
                    SectionContent.content_data.ilike(f'%{query}%')
                )
            ).subquery()
            
            search_conditions.append(ChapterSection.id.in_(content_search))
            
            # Combine search conditions
            base_query = ChapterSection.query.join(ChemistryChapter).filter(
                ChemistryChapter.is_active == True,
                or_(*search_conditions)
            )
            
            # Filter by chapter if specified
            if chapter_slug:
                base_query = base_query.filter(ChemistryChapter.slug == chapter_slug)
            
            # Execute search
            results = base_query.order_by(
                ChapterSection.order_index
            ).limit(limit).all()
            
            # Format results
            formatted_results = []
            for section in results:
                # Get snippet of matching content
                snippet = get_content_snippet(section, query)
                
                formatted_results.append({
                    'section_id': section.section_id,
                    'title': section.title,
                    'chapter_title': section.chapter.title,
                    'chapter_slug': section.chapter.slug,
                    'heading_level': section.heading_level,
                    'page_number': section.page_number,
                    'snippet': snippet,
                    'url': url_for('view_section', 
                                 chapter_slug=section.chapter.slug, 
                                 section_id=section.section_id)
                })
            
            return jsonify({
                'results': formatted_results,
                'total': len(formatted_results),
                'query': query
            })
            
        except Exception as e:
            error_logger.error(f"Error searching notes: {e}")
            return jsonify({'error': 'Search failed'}), 500
    
    @app.route("/api/notes/<chapter_slug>/sections")
    @login_required
    def get_chapter_sections(chapter_slug):
        """Get all sections for a chapter (API endpoint)"""
        try:
            chapter = ChemistryChapter.query.filter_by(
                slug=chapter_slug, 
                is_active=True
            ).first()
            
            if not chapter:
                return jsonify({'error': 'Chapter not found'}), 404
            
            sections = ChapterSection.query.filter_by(
                chapter_id=chapter.id
            ).order_by(ChapterSection.order_index).all()
            
            sections_data = []
            for section in sections:
                sections_data.append({
                    'section_id': section.section_id,
                    'title': section.title,
                    'heading_level': section.heading_level,
                    'page_number': section.page_number,
                    'order_index': section.order_index,
                    'parent_section_id': section.parent_section_id,
                    'url': url_for('view_section', 
                                 chapter_slug=chapter_slug, 
                                 section_id=section.section_id)
                })
            
            return jsonify({
                'chapter': {
                    'title': chapter.title,
                    'slug': chapter.slug,
                    'chapter_number': chapter.chapter_number
                },
                'sections': sections_data
            })
            
        except Exception as e:
            error_logger.error(f"Error getting sections for {chapter_slug}: {e}")
            return jsonify({'error': 'Failed to load sections'}), 500

    @app.route("/chemistry-notes-images/<path:filename>")
    def serve_chemistry_notes_image(filename):
        """Serve chemistry notes images"""
        try:
            # Security check - validate filename to prevent directory traversal attacks
            if '..' in filename or filename.startswith('/'):
                abort(404)

            # Path to chemistry notes images directory
            images_dir = os.path.join(os.getcwd(), 'chemistry_notes_markdown', 'images')

            # Check if file exists
            file_path = os.path.join(images_dir, filename)
            if not os.path.exists(file_path):
                abort(404)

            return send_from_directory(images_dir, filename)
        except Exception as e:
            error_logger.error(f"Error serving chemistry notes image {filename}: {e}")
            abort(404)


def build_section_hierarchy(sections):
    """Build hierarchical structure of sections for navigation"""
    hierarchy = []
    section_map = {section.section_id: section for section in sections}
    
    for section in sections:
        if section.parent_section_id is None:
            # Top-level section
            hierarchy.append({
                'section': section,
                'children': get_section_children(section, section_map)
            })
    
    return hierarchy


def get_section_children(parent_section, section_map):
    """Recursively get children of a section"""
    children = []
    
    for section_id, section in section_map.items():
        if section.parent_section_id == parent_section.section_id:
            children.append({
                'section': section,
                'children': get_section_children(section, section_map)
            })
    
    # Sort children by order index
    children.sort(key=lambda x: x['section'].order_index)
    return children


def get_content_snippet(section, query, max_length=200):
    """Get a snippet of content around the search query"""
    content = section.content or ""
    
    # Find the query in the content (case insensitive)
    query_lower = query.lower()
    content_lower = content.lower()
    
    query_pos = content_lower.find(query_lower)
    
    if query_pos == -1:
        # Query not found in content, return beginning
        return content[:max_length] + "..." if len(content) > max_length else content
    
    # Calculate snippet boundaries
    start = max(0, query_pos - max_length // 2)
    end = min(len(content), query_pos + len(query) + max_length // 2)
    
    snippet = content[start:end]
    
    # Add ellipsis if needed
    if start > 0:
        snippet = "..." + snippet
    if end < len(content):
        snippet = snippet + "..."
    
    return snippet
