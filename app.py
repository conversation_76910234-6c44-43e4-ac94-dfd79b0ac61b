import os
import logging
from logging.handlers import RotatingFile<PERSON>and<PERSON>
from flask import Flask, session
from dotenv import load_dotenv

from models import db
from flask_session import Session
from flask_migrate import Migrate
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from werkzeug.middleware.proxy_fix import ProxyFix

from routes.utils import (
    register_template_filters,
    register_context_processors,
    register_logging_hooks,
    register_error_handlers,
    register_file_serving_routes,
    update_user_activity,
    login_required,
    error_logger,
    user_logger,
    app_logger
)

def setup_logging(app):
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    loggers_config = {
        'app': {'level': logging.INFO, 'filename': 'app.log'},
        'user_activity': {'level': logging.INFO, 'filename': 'user_activity.log'},
        'errors': {'level': logging.ERROR, 'filename': 'errors.log'}
    }

    for name, config in loggers_config.items():
        logger = logging.getLogger(name)
        logger.setLevel(config['level'])
        logger.propagate = False # Prevent duplicate logs if root logger is configured

        # File Handler
        file_handler = RotatingFileHandler(
            os.path.join(log_dir, config['filename']),
            maxBytes=10485760, backupCount=10
        )
        file_handler.setFormatter(log_format)
        logger.addHandler(file_handler)

        # Console Handler (for 'app' and 'errors' loggers only?)
        if name in ['app', 'errors']:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(log_format)
            logger.addHandler(console_handler)

    return True

def create_app(config_name=None): 
    """ Application factory """
    app = Flask(__name__)
    app.wsgi_app = ProxyFix(app.wsgi_app, x_for=1, x_proto=1, x_host=1)

    load_dotenv()
    setup_logging(app)

    if config_name is None: # none by default
        config_name = os.getenv('FLASK_ENV', 'development')
        
    app.config.from_object(f'config.{config_name.capitalize()}Config')

    os.makedirs(app.instance_path, exist_ok=True)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['SESSION_FILE_DIR'], exist_ok=True)

    db.init_app(app)
    Session(app)
    migrate = Migrate(app, db)

    limiter = Limiter(
        get_user_id_or_ip,
        app=app,
        storage_uri=os.getenv('REDIS_URL', "memory://"), # Use Redis if available, else memory
        strategy="fixed-window" # or "moving-window"
    )

    # Initialise apis
    from groq import Groq
    from mistralai import Mistral
    from supabase import create_client
    import google.generativeai as genai
    groq_client = Groq(api_key=os.getenv("GROQ_API_KEY"))
    mistral_client = Mistral(api_key=os.getenv("MISTRAL_API_KEY"))
    supabase_client = create_client(os.getenv("SUPABASE_URL"), os.getenv("SUPABASE_KEY"))
    genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
    gemini_grading_client = genai.GenerativeModel('gemini-2.5-flash-preview-05-20')

    # Register blueprints and routes
    from routes.core import register_core_routes
    from routes.vault import register_vault_routes
    from routes.admin import register_admin_routes
    from routes.groups import register_group_routes
    from routes.problemsets import register_problemset_routes
    from routes.api import register_api_routes
    from routes.review_api import register_review_api_routes
    from routes.onboarding import register_onboarding_routes
    from routes.dashboard import register_dashboard_routes
    from routes.attachment_handler import attachment_bp
    from routes.file_serve import serve_bp
    from routes.auth import register_auth_routes
    from routes.teacher import register_teacher_routes
    from routes.clarifications import register_clarification_routes
    from routes.utils import register_utility_routes
    from routes.chemistry_notes import register_chemistry_notes_routes
    
    app.register_blueprint(attachment_bp)
    app.register_blueprint(serve_bp, url_prefix='/serve')

    # Register hooks, filters, processors, error handlers
    register_template_filters(app)
    register_context_processors(app)
    register_logging_hooks(app)
    register_error_handlers(app, limiter)

    # Register routes
    register_file_serving_routes(app) # From utils

    # Authentication routes
    register_auth_routes(app, db, session, supabase_client)

    # Register route functions
    register_core_routes(app, db, session)
    register_vault_routes(app, db, session)
    register_admin_routes(app, db, session)
    register_group_routes(app, db, session)
    register_problemset_routes(app, db, session)
    register_onboarding_routes(app, db, session)
    register_dashboard_routes(app, db, session)
    register_api_routes(app, db, session, limiter, groq_client, mistral_client, gemini_grading_client)
    register_review_api_routes(app)
    register_teacher_routes(app, db, session)
    register_clarification_routes(app, db, session)
    register_utility_routes(app, db, session)
    register_chemistry_notes_routes(app, db, session)

    register_commands(app, db)

    # Register chemistry notes commands
    from commands.chemistry_notes_commands import register_chemistry_notes_commands
    register_chemistry_notes_commands(app)

    return app

def get_user_id_or_ip():
    """Key function for rate limiter: uses user_id if logged in, otherwise IP."""
    if 'user_id' in session:
        return str(session['user_id'])
    return get_remote_address()

def register_commands(app, db):
    import json
    from models import Subject, Topic

    @app.cli.command('seed')

    def init_db():
        '''
        Initialise DB with subject/topic data
        '''
        with open('content/subject_data.json', 'r') as f:
            data = json.load(f)

        subject_map = {}
        for subject_data in data['subjects']:
            subject = Subject.query.filter_by(syllabus=subject_data['syllabus']).first()
            if not subject:
                subject = Subject(name=subject_data['name'], syllabus=subject_data['syllabus'])
                db.session.add(subject)
                db.session.commit() # creates primary key
            subject_map[subject.name] = subject.id

        for topic_data in data['topics']:
            subject_id = subject_map.get(topic_data['subject'])
            if subject_id:
                topic = Topic.query.filter_by(name=topic_data['name']).first()
                if not topic:
                    topic = Topic(name=topic_data['name'], subject_id=subject_id)
                    db.session.add(topic)

        db.session.commit()

        print("DB init done")

app = create_app()

if __name__ == "__main__":
    # Use environment variables for host, port, debug
    host = os.getenv('FLASK_RUN_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_RUN_PORT', 8000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() in ['true', '1', 't']
    
    # Create database tables if they don't exist
    with app.app_context():
        try:
            db.create_all()
            app_logger.info("Database tables checked/created.")
        except Exception as e:
            error_logger.error(f"Error during initial db.create_all(): {e}")
    
    app.run(host=host, port=port, debug=debug, threaded=True)