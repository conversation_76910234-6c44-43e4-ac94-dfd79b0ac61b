"""
Chemistry Notes Management Commands
CLI commands for importing, updating, and managing chemistry notes.
"""

import os
import click
from flask.cli import with_appcontext
from sqlalchemy.exc import IntegrityError

from models import db, ChemistryChapter, ChapterSection, SectionContent
from utils.chemistry_notes_parser import ChemistryNotesParser


@click.group()
def chemistry_notes():
    """Chemistry notes management commands."""
    pass


@chemistry_notes.command()
@click.argument('notes_directory')
@click.option('--force', is_flag=True, help='Force reimport even if chapter exists')
@click.option('--dry-run', is_flag=True, help='Show what would be imported without actually importing')
@with_appcontext
def import_notes(notes_directory, force, dry_run):
    """Import chemistry notes from markdown files."""
    if not os.path.exists(notes_directory):
        click.echo(f"Error: Directory '{notes_directory}' does not exist.")
        return
    
    parser = ChemistryNotesParser()
    imported_count = 0
    skipped_count = 0
    error_count = 0
    
    # Find all markdown files
    markdown_files = []
    for filename in os.listdir(notes_directory):
        if filename.endswith('.md'):
            file_path = os.path.join(notes_directory, filename)
            markdown_files.append(file_path)
    
    if not markdown_files:
        click.echo(f"No markdown files found in '{notes_directory}'")
        return
    
    click.echo(f"Found {len(markdown_files)} markdown files to process...")
    
    for file_path in sorted(markdown_files):
        try:
            filename = os.path.basename(file_path)
            click.echo(f"\nProcessing: {filename}")
            
            # Parse the file
            chapter_info, sections = parser.parse_markdown_file(file_path)
            
            if dry_run:
                click.echo(f"  Would import: {chapter_info['title']}")
                click.echo(f"  Slug: {chapter_info['slug']}")
                click.echo(f"  Sections: {len(sections)}")
                continue
            
            # Check if chapter already exists
            existing_chapter = ChemistryChapter.query.filter_by(
                slug=chapter_info['slug']
            ).first()
            
            if existing_chapter and not force:
                click.echo(f"  Skipped: Chapter '{chapter_info['title']}' already exists (use --force to reimport)")
                skipped_count += 1
                continue
            
            # Delete existing chapter if force is enabled
            if existing_chapter and force:
                click.echo(f"  Removing existing chapter...")
                db.session.delete(existing_chapter)
                db.session.commit()
            
            # Create new chapter
            chapter = ChemistryChapter(
                title=chapter_info['title'],
                slug=chapter_info['slug'],
                chapter_number=chapter_info['chapter_number'],
                file_path=file_path,
                description=f"Chemistry notes for {chapter_info['title']}"
            )
            
            db.session.add(chapter)
            db.session.flush()  # Get the chapter ID
            
            # Import sections
            section_count = 0
            for section_data in sections:
                section = ChapterSection(
                    chapter_id=chapter.id,
                    section_id=section_data.section_id,
                    title=section_data.title,
                    heading_level=section_data.heading_level,
                    content=section_data.content,
                    raw_content=section_data.raw_content,
                    page_number=section_data.page_number,
                    order_index=section_data.order_index,
                    parent_section_id=None  # We'll handle hierarchy later if needed
                )
                
                db.session.add(section)
                db.session.flush()  # Get the section ID
                
                # Process content blocks for better search
                content_blocks = parser.process_content_blocks(section_data)
                for block in content_blocks:
                    content = SectionContent(
                        section_id=section.id,
                        content_type=block['type'],
                        content_data=block['content'],
                        search_text=block.get('search_text', parser.generate_search_text(block['content'])),
                        order_index=block['order'],
                        meta_data=block.get('meta_data', {})
                    )
                    db.session.add(content)
                
                section_count += 1
            
            db.session.commit()
            
            click.echo(f"  ✓ Imported: {chapter_info['title']} ({section_count} sections)")
            imported_count += 1
            
        except Exception as e:
            db.session.rollback()
            click.echo(f"  ✗ Error importing {filename}: {str(e)}")
            error_count += 1
    
    if not dry_run:
        click.echo(f"\nImport complete:")
        click.echo(f"  Imported: {imported_count} chapters")
        click.echo(f"  Skipped: {skipped_count} chapters")
        click.echo(f"  Errors: {error_count} chapters")
    else:
        click.echo(f"\nDry run complete. Would import {len(markdown_files)} files.")


@chemistry_notes.command()
@click.option('--chapter-slug', help='Update specific chapter by slug')
@with_appcontext
def update_search_index(chapter_slug):
    """Update search index for chemistry notes."""
    parser = ChemistryNotesParser()
    
    if chapter_slug:
        chapters = ChemistryChapter.query.filter_by(slug=chapter_slug).all()
        if not chapters:
            click.echo(f"Chapter '{chapter_slug}' not found.")
            return
    else:
        chapters = ChemistryChapter.query.all()
    
    click.echo(f"Updating search index for {len(chapters)} chapters...")
    
    for chapter in chapters:
        click.echo(f"Processing: {chapter.title}")
        
        sections = ChapterSection.query.filter_by(chapter_id=chapter.id).all()
        
        for section in sections:
            # Delete existing content blocks
            SectionContent.query.filter_by(section_id=section.id).delete()
            
            # Recreate content blocks with updated search text
            if section.content:
                # Create a mock section data object
                from utils.chemistry_notes_parser import ParsedSection
                section_data = ParsedSection(
                    section_id=section.section_id,
                    title=section.title,
                    heading_level=section.heading_level,
                    content=section.content,
                    raw_content=section.raw_content,
                    page_number=section.page_number,
                    order_index=section.order_index,
                    parent_section_id=None
                )
                
                content_blocks = parser.process_content_blocks(section_data)
                for block in content_blocks:
                    content = SectionContent(
                        section_id=section.id,
                        content_type=block['type'],
                        content_data=block['content'],
                        search_text=block.get('search_text', parser.generate_search_text(block['content'])),
                        order_index=block['order'],
                        meta_data=block.get('meta_data', {})
                    )
                    db.session.add(content)
        
        db.session.commit()
        click.echo(f"  ✓ Updated search index for {len(sections)} sections")
    
    click.echo("Search index update complete.")


@chemistry_notes.command()
@with_appcontext
def list_chapters():
    """List all imported chemistry chapters."""
    chapters = ChemistryChapter.query.order_by(
        ChemistryChapter.chapter_number,
        ChemistryChapter.title
    ).all()
    
    if not chapters:
        click.echo("No chemistry chapters found.")
        return
    
    click.echo(f"Found {len(chapters)} chemistry chapters:\n")
    
    for chapter in chapters:
        section_count = ChapterSection.query.filter_by(chapter_id=chapter.id).count()
        status = "✓ Active" if chapter.is_active else "✗ Inactive"
        
        click.echo(f"  {chapter.chapter_number or 'N/A':<4} {chapter.title:<40} "
                  f"({section_count} sections) [{status}]")
        click.echo(f"       Slug: {chapter.slug}")
        click.echo(f"       File: {chapter.file_path}")
        click.echo()


@chemistry_notes.command()
@click.argument('chapter_slug')
@with_appcontext
def delete_chapter(chapter_slug):
    """Delete a chemistry chapter and all its sections."""
    chapter = ChemistryChapter.query.filter_by(slug=chapter_slug).first()
    
    if not chapter:
        click.echo(f"Chapter '{chapter_slug}' not found.")
        return
    
    section_count = ChapterSection.query.filter_by(chapter_id=chapter.id).count()
    
    if not click.confirm(f"Delete chapter '{chapter.title}' and its {section_count} sections?"):
        click.echo("Deletion cancelled.")
        return
    
    try:
        db.session.delete(chapter)
        db.session.commit()
        click.echo(f"✓ Deleted chapter '{chapter.title}' and {section_count} sections.")
    except Exception as e:
        db.session.rollback()
        click.echo(f"✗ Error deleting chapter: {str(e)}")


@chemistry_notes.command()
@click.argument('chapter_slug')
@click.option('--active/--inactive', default=True, help='Set chapter active status')
@with_appcontext
def set_status(chapter_slug, active):
    """Set the active status of a chemistry chapter."""
    chapter = ChemistryChapter.query.filter_by(slug=chapter_slug).first()
    
    if not chapter:
        click.echo(f"Chapter '{chapter_slug}' not found.")
        return
    
    chapter.is_active = active
    db.session.commit()
    
    status = "active" if active else "inactive"
    click.echo(f"✓ Set chapter '{chapter.title}' to {status}.")


@chemistry_notes.command()
@with_appcontext
def stats():
    """Show statistics about chemistry notes."""
    total_chapters = ChemistryChapter.query.count()
    active_chapters = ChemistryChapter.query.filter_by(is_active=True).count()
    total_sections = ChapterSection.query.count()
    total_content_blocks = SectionContent.query.count()
    
    # Content type breakdown
    content_types = db.session.query(
        SectionContent.content_type,
        db.func.count(SectionContent.id)
    ).group_by(SectionContent.content_type).all()
    
    click.echo("Chemistry Notes Statistics:")
    click.echo(f"  Total Chapters: {total_chapters}")
    click.echo(f"  Active Chapters: {active_chapters}")
    click.echo(f"  Total Sections: {total_sections}")
    click.echo(f"  Total Content Blocks: {total_content_blocks}")
    
    if content_types:
        click.echo("\nContent Type Breakdown:")
        for content_type, count in content_types:
            click.echo(f"  {content_type.title()}: {count}")
    
    # Average sections per chapter
    if total_chapters > 0:
        avg_sections = total_sections / total_chapters
        click.echo(f"\nAverage Sections per Chapter: {avg_sections:.1f}")


def register_chemistry_notes_commands(app):
    """Register chemistry notes commands with the Flask app."""
    app.cli.add_command(chemistry_notes)
