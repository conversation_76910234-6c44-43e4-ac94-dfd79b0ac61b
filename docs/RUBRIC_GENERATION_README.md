# Rubric Generation System

This document describes the enhanced rubric generation system with parallel processing and database storage capabilities.

## Overview

The system generates AI-powered rubrics for **Short Answer Question (SAQ) parts only** using Google's Gemini AI, with support for:
- **SAQ-only processing** (excludes MCQ and image-based questions)
- **Subject filtering** to process specific subjects
- **Parallel processing** for faster batch operations
- **Database storage** of generated rubrics
- **Incremental processing** (skip already processed parts)
- **Thread-safe operations** for concurrent execution
- **Comprehensive CLI tools** for management

## Database Schema

The system adds three new columns to the `Part` model:

```sql
ALTER TABLE parts ADD COLUMN content_rubric TEXT;     -- JSON string of content requirements
ALTER TABLE parts ADD COLUMN scoring_rubric TEXT;    -- JSON string of bit-mapped scoring rubrics  
ALTER TABLE parts ADD COLUMN rubrics_generated_at DATETIME; -- Timestamp when rubrics were generated
```

## Generated Rubric Structure

### Content Rubric
A JSON object mapping requirement IDs to specific content requirements:
```json
{
  "1": "The equation must correctly show B³⁺ as the reactant and B⁴⁺ with an electron (e⁻) as products.",
  "2": "All species (B³⁺, B⁴⁺, e⁻) must be indicated in the gaseous state (g).",
  "3": "The equation must use a single forward arrow (→) to represent the transformation."
}
```

### Scoring Rubric
A JSON object mapping bit combinations to scores (bit-mapped scoring):
```json
{
  "0": 0,  // No requirements met
  "1": 1,  // Only requirement 1 met (bit 0)
  "2": 0,  // Only requirement 2 met (bit 1) 
  "3": 1,  // Requirements 1,2 met (bits 0,1)
  "4": 0,  // Only requirement 3 met (bit 2)
  "5": 1,  // Requirements 1,3 met (bits 0,2)
  "6": 0,  // Requirements 2,3 met (bits 1,2)
  "7": 2   // All requirements met (bits 0,1,2)
}
```

## Usage

### Command Line Interface

#### Basic Usage
```bash
# List available subjects and their SAQ statistics
python preprocess.py --list-subjects

# Generate rubrics for all SAQ parts without existing rubrics (4 workers)
python preprocess.py

# Test mode - process only first SAQ part
python preprocess.py --test

# Filter by specific subject (e.g., Chemistry - subject ID 3)
python preprocess.py --subject-id 3

# Test mode for specific subject
python preprocess.py --test --subject-id 3

# Specify number of workers
python preprocess.py --workers 8

# Process specific parts
python preprocess.py --part-ids 1 2 3 4 5

# Force regeneration of existing rubrics
python preprocess.py --no-skip-existing

# Generate rubrics for Chemistry SAQ parts only
python preprocess.py --subject-id 3 --workers 8
```

#### Management Script
```bash
# List available subjects and their SAQ statistics
python run_rubric_generation.py subjects

# Generate rubrics with options
python run_rubric_generation.py generate --workers 8
python run_rubric_generation.py generate --part-ids 1 2 3 --force

# Generate rubrics for specific subject (Chemistry)
python run_rubric_generation.py generate --workers 8 --subject-id 3

# View statistics (all subjects)
python run_rubric_generation.py stats

# View statistics for specific subject
python run_rubric_generation.py stats --subject-id 3

# List all SAQ parts with rubrics
python run_rubric_generation.py list

# List SAQ parts with rubrics for specific subject
python run_rubric_generation.py list --subject-id 3

# Show rubrics for specific part
python run_rubric_generation.py show 1

# Quick test (first SAQ part)
python run_rubric_generation.py test

# Quick test for specific subject
python run_rubric_generation.py test --subject-id 3
```

### Programmatic Usage

```python
from preprocess import PlatoPreprocess

# Initialize with 4 workers
plato = PlatoPreprocess(max_workers=4)

# Process all parts without rubrics
plato.work()

# Process specific parts
plato.work(part_ids=[1, 2, 3], skip_existing=False)

# Process single part
part = Part.query.get(1)
result = plato.process_single_part(part)

# Retrieve rubrics from database
rubrics = plato.get_rubrics_from_db(part_id=1)

# List parts with rubrics
parts_with_rubrics = plato.list_parts_with_rubrics()
```

## Performance Features

### Parallel Processing
- Configurable number of worker threads (default: 4)
- Thread-safe database operations using locks
- Concurrent API calls to Gemini AI
- Progress tracking and error handling

### Incremental Processing
- Skip parts that already have rubrics (default behavior)
- Force regeneration with `--no-skip-existing` flag
- Resume interrupted batch operations

### Error Handling
- Graceful handling of API failures
- Database transaction rollback on errors
- Detailed logging of failures
- Continue processing other parts on individual failures

## Performance Metrics

Based on testing:
- **Single part**: ~30-35 seconds (including API calls)
- **4 parts parallel**: ~43 seconds total (4 workers)
- **Speedup**: ~3x faster than sequential processing
- **Database operations**: Thread-safe with minimal overhead

## Database Integration

### Saving Rubrics
```python
def save_rubrics_to_db(self, part_id, content_rubrics, scoring_rubrics):
    """Save generated rubrics to the database in a thread-safe manner"""
    with self.db_lock:
        with app.app_context():
            part = Part.query.get(part_id)
            part.content_rubric = json.dumps(content_rubrics)
            part.scoring_rubric = json.dumps(scoring_rubrics)
            part.rubrics_generated_at = datetime.now()
            db.session.commit()
```

### Retrieving Rubrics
```python
def get_rubrics_from_db(self, part_id):
    """Retrieve rubrics from database for a given part"""
    with app.app_context():
        part = Part.query.get(part_id)
        if part and part.content_rubric and part.scoring_rubric:
            return {
                'content_rubric': json.loads(part.content_rubric),
                'scoring_rubric': json.loads(part.scoring_rubric),
                'generated_at': part.rubrics_generated_at
            }
```

## Migration

The system includes a database migration to add the new columns:

```bash
# Generate migration
flask db migrate -m "Add rubric columns to Part model"

# Apply migration
flask db upgrade
```

## Monitoring and Statistics

### View Statistics
```bash
python run_rubric_generation.py stats
```

Output:
```
Rubric Generation Statistics
==============================
Total parts: 370
Parts with rubrics: 5
Parts without rubrics: 365
Completion rate: 1.4%
```

### List Generated Rubrics
```bash
python run_rubric_generation.py list
```

## Error Handling

The system handles various error conditions:
- **Pinecone API failures**: Continues without context
- **Gemini API failures**: Logs error and skips part
- **Database errors**: Rollback transaction and continue
- **Threading issues**: Thread-safe operations with locks

## Future Enhancements

Potential improvements:
1. **Batch API calls** to reduce latency
2. **Caching** of Pinecone context queries
3. **Retry logic** for failed API calls
4. **Progress persistence** for very large batches
5. **Quality metrics** for generated rubrics
