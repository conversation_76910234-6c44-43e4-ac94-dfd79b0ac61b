#!/usr/bin/env python3
"""
Script to run rubric generation with various options
"""

from preprocess import PlatoPreprocess
from models import Part, db, Subject, Topic, Question
from app import app
import sys

def show_usage():
    """Show usage information"""
    print("""
Usage: python run_rubric_generation.py [command] [options]

Commands:
    generate [--workers N] [--part-ids ID1 ID2 ...] [--force] [--subject-id N]
        Generate rubrics for short answer question parts
        --workers N: Number of parallel workers (default: 4)
        --part-ids: Specific part IDs to process (default: all SAQ parts without rubrics)
        --force: Process even parts that already have rubrics
        --subject-id N: Filter by subject ID (use subjects command to see available)

    subjects
        List available subjects and their SAQ statistics

    list [--subject-id N]
        List all parts with generated rubrics
        --subject-id N: Filter by subject ID

    show PART_ID
        Show rubrics for a specific part

    stats [--subject-id N]
        Show statistics about rubric generation
        --subject-id N: Filter by subject ID

    test [--subject-id N]
        Run a quick test on the first SAQ part
        --subject-id N: Filter by subject ID

Examples:
    python run_rubric_generation.py subjects
    python run_rubric_generation.py generate --workers 8 --subject-id 3
    python run_rubric_generation.py generate --part-ids 1 2 3 --force
    python run_rubric_generation.py stats --subject-id 3
    python run_rubric_generation.py show 1
    python run_rubric_generation.py list --subject-id 3
""")

def generate_rubrics(workers=4, part_ids=None, force=False, subject_id=None):
    """Generate rubrics with specified options"""
    print(f"Generating rubrics for SAQ parts with {workers} workers...")
    if part_ids:
        print(f"Processing specific parts: {part_ids}")
    if subject_id:
        print(f"Filtering by subject ID: {subject_id}")
    if force:
        print("Force mode: will regenerate existing rubrics")

    plato = PlatoPreprocess(max_workers=workers)
    plato.work(part_ids=part_ids, skip_existing=not force, subject_id=subject_id)

def list_subjects():
    """List available subjects and their SAQ statistics"""
    plato = PlatoPreprocess()
    subjects = plato.list_available_subjects()

    if not subjects:
        print("No subjects found.")
        return

    print("Available Subjects (Short Answer Questions only):")
    print("=" * 70)
    print(f"{'ID':<3} {'Name':<20} {'Syllabus':<8} {'SAQ Parts':<10} {'With Rubrics':<12} {'Complete %':<10}")
    print("-" * 70)
    for subject in subjects:
        print(f"{subject['id']:<3} {subject['name']:<20} {subject['syllabus']:<8} {subject['saq_parts']:<10} {subject['saq_with_rubrics']:<12} {subject['completion_rate']:<10.1f}")

def list_parts(subject_id=None):
    """List all parts with rubrics"""
    plato = PlatoPreprocess()
    parts = plato.list_parts_with_rubrics()

    # Filter by subject if specified
    if subject_id and parts:
        from models import Part, Question, Topic, Subject
        from app import app
        with app.app_context():
            # Get part IDs for the specified subject
            subject_part_ids = set()
            subject_parts = Part.query.join(Question).join(Topic).join(Subject).filter(
                Subject.id == subject_id,
                Part.input_type == 'saq'
            ).all()
            subject_part_ids = {part.id for part in subject_parts}

            # Filter the parts list
            parts = [part for part in parts if part['id'] in subject_part_ids]

    if not parts:
        subject_msg = f" for subject {subject_id}" if subject_id else ""
        print(f"No SAQ parts with rubrics found{subject_msg}.")
        return

    subject_msg = f" for subject {subject_id}" if subject_id else ""
    print(f"Found {len(parts)} SAQ parts with rubrics{subject_msg}:")
    print("-" * 80)
    for part in parts:
        print(f"ID: {part['id']:3d} | Score: {part['score']:2d} | Generated: {part['generated_at']}")
        print(f"     {part['description']}")
        print()

def show_part_rubrics(part_id):
    """Show rubrics for a specific part"""
    plato = PlatoPreprocess()
    rubrics = plato.get_rubrics_from_db(part_id)
    
    if not rubrics:
        print(f"No rubrics found for part {part_id}")
        return
    
    print(f"Rubrics for Part {part_id}")
    print("=" * 50)
    print(f"Generated at: {rubrics['generated_at']}")
    print()
    
    print("Content Requirements:")
    print("-" * 20)
    for req_id, requirement in rubrics['content_rubric'].items():
        print(f"  {req_id}: {requirement}")
    print()
    
    print("Scoring Rubric (bit-mapped):")
    print("-" * 30)
    for bit_combo, score in rubrics['scoring_rubric'].items():
        print(f"  Combination {bit_combo}: {score} points")

def show_stats(subject_id=None):
    """Show statistics about rubric generation"""
    with app.app_context():
        # Base query for SAQ parts only
        base_query = Part.query.filter(Part.input_type == 'saq')
        rubrics_query = Part.query.filter(
            Part.input_type == 'saq',
            Part.content_rubric.isnot(None),
            Part.scoring_rubric.isnot(None)
        )

        # Filter by subject if specified
        if subject_id:
            base_query = base_query.join(Question).join(Topic).join(Subject).filter(Subject.id == subject_id)
            rubrics_query = rubrics_query.join(Question).join(Topic).join(Subject).filter(Subject.id == subject_id)

            # Get subject name
            subject = Subject.query.get(subject_id)
            subject_name = subject.name if subject else f"Subject {subject_id}"

        total_parts = base_query.count()
        parts_with_rubrics = rubrics_query.count()

        title = f"Rubric Generation Statistics (SAQ only)"
        if subject_id:
            title += f" - {subject_name}"

        print(title)
        print("=" * len(title))
        print(f"Total SAQ parts: {total_parts}")
        print(f"SAQ parts with rubrics: {parts_with_rubrics}")
        print(f"SAQ parts without rubrics: {total_parts - parts_with_rubrics}")
        print(f"Completion rate: {(parts_with_rubrics/total_parts*100):.1f}%" if total_parts > 0 else "N/A")

def run_test(subject_id=None):
    """Run a quick test"""
    subject_msg = f" for subject {subject_id}" if subject_id else ""
    print(f"Running quick test on first SAQ part{subject_msg}...")
    plato = PlatoPreprocess(max_workers=1)

    # Find first SAQ part
    with app.app_context():
        query = Part.query.filter(Part.input_type == 'saq')
        if subject_id:
            query = query.join(Question).join(Topic).join(Subject).filter(Subject.id == subject_id)
        first_part = query.first()

        if first_part:
            plato.work(part_ids=[first_part.id], skip_existing=False, subject_id=subject_id)
        else:
            print(f"No SAQ parts found{subject_msg}")
            return

def main():
    if len(sys.argv) < 2:
        show_usage()
        return

    command = sys.argv[1].lower()

    if command == "generate":
        workers = 4
        part_ids = None
        force = False
        subject_id = None

        i = 2
        while i < len(sys.argv):
            if sys.argv[i] == "--workers" and i + 1 < len(sys.argv):
                workers = int(sys.argv[i + 1])
                i += 2
            elif sys.argv[i] == "--part-ids":
                part_ids = []
                i += 1
                while i < len(sys.argv) and not sys.argv[i].startswith("--"):
                    part_ids.append(int(sys.argv[i]))
                    i += 1
            elif sys.argv[i] == "--force":
                force = True
                i += 1
            elif sys.argv[i] == "--subject-id" and i + 1 < len(sys.argv):
                subject_id = int(sys.argv[i + 1])
                i += 2
            else:
                i += 1

        generate_rubrics(workers, part_ids, force, subject_id)

    elif command == "subjects":
        list_subjects()

    elif command == "list":
        subject_id = None
        i = 2
        while i < len(sys.argv):
            if sys.argv[i] == "--subject-id" and i + 1 < len(sys.argv):
                subject_id = int(sys.argv[i + 1])
                i += 2
            else:
                i += 1
        list_parts(subject_id)

    elif command == "show":
        if len(sys.argv) < 3:
            print("Please specify a part ID")
            return
        part_id = int(sys.argv[2])
        show_part_rubrics(part_id)

    elif command == "stats":
        subject_id = None
        i = 2
        while i < len(sys.argv):
            if sys.argv[i] == "--subject-id" and i + 1 < len(sys.argv):
                subject_id = int(sys.argv[i + 1])
                i += 2
            else:
                i += 1
        show_stats(subject_id)

    elif command == "test":
        subject_id = None
        i = 2
        while i < len(sys.argv):
            if sys.argv[i] == "--subject-id" and i + 1 < len(sys.argv):
                subject_id = int(sys.argv[i + 1])
                i += 2
            else:
                i += 1
        run_test(subject_id)

    else:
        print(f"Unknown command: {command}")
        show_usage()

if __name__ == "__main__":
    main()
