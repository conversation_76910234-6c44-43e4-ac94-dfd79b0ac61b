# Chemistry Notes System

A comprehensive system for serving chemistry notes with section tagging, hyperlinking, and search functionality. Designed to support future RAG (Retrieval-Augmented Generation) integration.

## Features

### 🏷️ **Section Tagging System**
- **Automatic tagging** of all headings and sections
- **Unique IDs** for every section (e.g., `alkanes-h1-1`, `acid-base-page-5`)
- **Hierarchical structure** with parent-child relationships
- **Hyperlink support** for direct section access

### 🔍 **Advanced Search**
- **Full-text search** across all notes content
- **Section-specific search** within individual chapters
- **Content type filtering** (text, equations, images, tables)
- **Snippet generation** with query highlighting

### 📱 **Responsive Interface**
- **Modern UI** with glassmorphism design
- **Table of Contents** with collapsible navigation
- **Mobile-friendly** responsive design
- **Keyboard shortcuts** and accessibility features

### 🔗 **Hyperlinking & Navigation**
- **Direct section links** for sharing specific content
- **Cross-references** between related sections
- **Breadcrumb navigation** for easy orientation
- **Previous/Next section** navigation

## System Architecture

### Database Models

#### `ChemistryChapter`
- Stores chapter metadata (title, slug, chapter number)
- Tracks file paths and update timestamps
- Manages active/inactive status

#### `ChapterSection`
- Individual sections with unique IDs
- Hierarchical relationships (parent/child)
- Content storage (processed and raw markdown)
- Page number and order tracking

#### `SectionContent`
- Typed content blocks (text, image, equation, table)
- Search-optimized text extraction
- Metadata storage for rich content

### URL Structure

```
/notes/                           # Notes index page
/notes/<chapter-slug>/            # Chapter view
/notes/<chapter-slug>/<section-id> # Direct section access
/api/notes/search                 # Search API
/api/notes/<chapter-slug>/sections # Chapter sections API
```

### Tagging Format

**Section IDs**: `{chapter-slug}-{type}-{index}`

Examples:
- `alkanes-h1-1` - First H1 heading in Alkanes chapter
- `acid-base-h2-3` - Third H2 heading in Acid-Base chapter
- `alkanes-page-5` - Page 5 marker in Alkanes chapter

## Installation & Setup

### 1. Install Dependencies

```bash
pip install python-slugify
```

### 2. Database Migration

```bash
flask db migrate -m "Add chemistry notes models"
flask db upgrade
```

### 3. Import Notes

```bash
# Import all notes from directory
flask chemistry-notes import-notes chemistry_notes_markdown/

# Force reimport existing notes
flask chemistry-notes import-notes chemistry_notes_markdown/ --force

# Dry run to see what would be imported
flask chemistry-notes import-notes chemistry_notes_markdown/ --dry-run
```

## Usage

### Command Line Interface

```bash
# List all imported chapters
flask chemistry-notes list-chapters

# Show system statistics
flask chemistry-notes stats

# Update search index
flask chemistry-notes update-search-index

# Delete a chapter
flask chemistry-notes delete-chapter alkanes

# Set chapter status
flask chemistry-notes set-status alkanes --active
flask chemistry-notes set-status alkanes --inactive
```

### Web Interface

1. **Browse Notes**: Visit `/notes` to see all available chapters
2. **Search**: Use the global search bar to find content across all notes
3. **Navigate**: Click on chapters to view full content with table of contents
4. **Link Sections**: Use the link icon next to any section to copy its direct URL
5. **Chapter Search**: Use chapter-specific search within individual notes

### API Endpoints

#### Search Notes
```http
GET /api/notes/search?q=chemical+bonding&chapter=alkanes&limit=20
```

Response:
```json
{
  "results": [
    {
      "section_id": "alkanes-h2-3",
      "title": "Chemical Bonding in Alkanes",
      "chapter_title": "Alkanes",
      "chapter_slug": "alkanes",
      "heading_level": 2,
      "page_number": 5,
      "snippet": "Chemical bonding in alkanes involves...",
      "url": "/notes/alkanes/alkanes-h2-3"
    }
  ],
  "total": 1,
  "query": "chemical bonding"
}
```

#### Get Chapter Sections
```http
GET /api/notes/alkanes/sections
```

Response:
```json
{
  "chapter": {
    "title": "Alkanes",
    "slug": "alkanes",
    "chapter_number": "10"
  },
  "sections": [
    {
      "section_id": "alkanes-h1-1",
      "title": "Introduction",
      "heading_level": 1,
      "page_number": 1,
      "order_index": 0,
      "parent_section_id": null,
      "url": "/notes/alkanes/alkanes-h1-1"
    }
  ]
}
```

## File Structure

```
chemistry_notes_markdown/          # Source markdown files
├── 1a Mole Concept and Stoichiometry.md
├── 10 Alkanes.md
├── 15 Acid-Base Equilibria.md
└── images/                       # Associated images
    ├── 10_Alkanes_img-0.jpeg
    └── ...

utils/
└── chemistry_notes_parser.py     # Markdown parser with tagging

routes/
└── chemistry_notes.py           # Flask routes for notes

templates/notes/
├── index.html                   # Notes listing page
├── chapter.html                 # Chapter view template
└── _toc_item.html              # Table of contents component

commands/
└── chemistry_notes_commands.py  # CLI management commands
```

## RAG Integration Preparation

The system is designed to support future RAG integration:

### 1. **Structured Content**
- Each section has a unique, stable ID
- Content is pre-processed and cleaned for embedding
- Hierarchical relationships are preserved

### 2. **Search Infrastructure**
- Full-text search with ranking
- Content type classification
- Metadata preservation for context

### 3. **API-Ready**
- RESTful endpoints for content retrieval
- JSON responses with structured data
- Pagination and filtering support

### 4. **Embedding-Friendly Format**
```python
# Example: Prepare content for embedding
def get_section_for_embedding(section_id):
    section = ChapterSection.query.filter_by(section_id=section_id).first()
    return {
        'id': section.section_id,
        'title': section.title,
        'content': section.content,
        'context': {
            'chapter': section.chapter.title,
            'page': section.page_number,
            'level': section.heading_level
        },
        'url': f"/notes/{section.chapter.slug}/{section.section_id}"
    }
```

## Testing

Run the test script to verify the system:

```bash
python test_chemistry_notes.py
```

This will:
- Test the markdown parser
- Show tagging examples
- Demonstrate content processing
- Verify search text generation

## Future Enhancements

1. **RAG Integration**: Connect with vector databases for semantic search
2. **Cross-References**: Automatic linking between related sections
3. **Annotations**: User notes and highlights on sections
4. **Export Features**: PDF generation with preserved links
5. **Version Control**: Track changes to notes over time
6. **Collaborative Features**: Shared annotations and discussions

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure `python-slugify` is installed
2. **Database Errors**: Run migrations before importing
3. **File Not Found**: Check that markdown files exist in specified directory
4. **Search Not Working**: Update search index after importing

### Debug Commands

```bash
# Check system status
flask chemistry-notes stats

# Rebuild search index
flask chemistry-notes update-search-index

# Test parser
python test_chemistry_notes.py
```
