"""
Chemistry Notes Parser
Processes markdown files and generates tagged sections for hyperlinking and RAG.
"""

import re
import os
import markdown
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from slugify import slugify
import json


@dataclass
class ParsedSection:
    """Represents a parsed section from chemistry notes"""
    section_id: str
    title: str
    heading_level: int
    content: str
    raw_content: str
    page_number: Optional[int]
    order_index: int
    parent_section_id: Optional[str]


class ChemistryNotesParser:
    """Parser for chemistry notes markdown files with section tagging"""
    
    def __init__(self):
        self.heading_pattern = re.compile(r'^(#{1,6})\s+(.+)$', re.MULTILINE)
        self.page_pattern = re.compile(r'^##\s+Page\s+(\d+)$', re.MULTILINE)
        self.image_pattern = re.compile(r'!\[([^\]]*)\]\(([^)]+)\)')
        
    def generate_chapter_slug(self, title: str) -> str:
        """Generate URL-friendly slug from chapter title"""
        # Remove chapter numbers and clean up
        clean_title = re.sub(r'^\d+[a-z]?\s+', '', title)
        return slugify(clean_title, max_length=50)
    
    def generate_section_id(self, chapter_slug: str, section_type: str, index: int) -> str:
        """Generate unique section ID for hyperlinking"""
        return f"{chapter_slug}-{section_type}-{index}"
    
    def extract_chapter_info(self, file_path: str) -> Dict[str, str]:
        """Extract chapter metadata from file path and content"""
        filename = os.path.basename(file_path)
        
        # Extract chapter number and title from filename
        match = re.match(r'^(\d+[a-z]?)\s+(.+)\.md$', filename)
        if match:
            chapter_number = match.group(1)
            title = match.group(2)
        else:
            chapter_number = None
            title = filename.replace('.md', '')
        
        slug = self.generate_chapter_slug(title)
        
        return {
            'title': title,
            'chapter_number': chapter_number,
            'slug': slug,
            'file_path': file_path
        }
    
    def parse_markdown_file(self, file_path: str) -> Tuple[Dict[str, str], List[ParsedSection]]:
        """Parse a markdown file and extract tagged sections"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        chapter_info = self.extract_chapter_info(file_path)
        sections = self._parse_sections(content, chapter_info['slug'])
        
        return chapter_info, sections
    
    def _parse_sections(self, content: str, chapter_slug: str) -> List[ParsedSection]:
        """Parse content into tagged sections"""
        sections = []
        lines = content.split('\n')
        
        current_section = None
        section_content = []
        order_index = 0
        heading_counters = {'h1': 0, 'h2': 0, 'h3': 0, 'h4': 0, 'h5': 0, 'h6': 0}
        page_counters = {'page': 0}
        current_page = None
        
        for line_num, line in enumerate(lines):
            # Check for page markers
            page_match = self.page_pattern.match(line)
            if page_match:
                current_page = int(page_match.group(1))
                page_counters['page'] += 1
                
                # Save previous section if exists
                if current_section:
                    current_section.content = '\n'.join(section_content).strip()
                    current_section.raw_content = current_section.content
                    sections.append(current_section)
                
                # Create page section
                section_id = self.generate_section_id(chapter_slug, 'page', page_counters['page'])
                current_section = ParsedSection(
                    section_id=section_id,
                    title=f"Page {current_page}",
                    heading_level=2,
                    content="",
                    raw_content="",
                    page_number=current_page,
                    order_index=order_index,
                    parent_section_id=None
                )
                order_index += 1
                section_content = []
                continue
            
            # Check for headings
            heading_match = self.heading_pattern.match(line)
            if heading_match:
                # Save previous section if exists
                if current_section:
                    current_section.content = '\n'.join(section_content).strip()
                    current_section.raw_content = current_section.content
                    sections.append(current_section)
                
                # Parse new heading
                heading_level = len(heading_match.group(1))
                title = heading_match.group(2).strip()
                
                # Update counters - don't reset, just increment
                heading_type = f'h{heading_level}'
                heading_counters[heading_type] += 1
                
                # Generate section ID
                section_id = self.generate_section_id(
                    chapter_slug, 
                    heading_type, 
                    heading_counters[heading_type]
                )
                
                # Determine parent section
                parent_section_id = self._find_parent_section(sections, heading_level)
                
                current_section = ParsedSection(
                    section_id=section_id,
                    title=title,
                    heading_level=heading_level,
                    content="",
                    raw_content="",
                    page_number=current_page,
                    order_index=order_index,
                    parent_section_id=parent_section_id
                )
                order_index += 1
                section_content = []
            else:
                # Add line to current section content
                if line.strip():  # Only add non-empty lines
                    section_content.append(line)
        
        # Save final section
        if current_section:
            current_section.content = '\n'.join(section_content).strip()
            current_section.raw_content = current_section.content
            sections.append(current_section)
        
        return sections
    
    def _find_parent_section(self, sections: List[ParsedSection], current_level: int) -> Optional[str]:
        """Find the parent section for the current heading level"""
        if not sections:
            return None
        
        # Look backwards for a section with a lower heading level
        for section in reversed(sections):
            if section.heading_level < current_level:
                return section.section_id
        
        return None
    
    def process_content_blocks(self, section: ParsedSection) -> List[Dict]:
        """Process section content into typed blocks for better search indexing"""
        content_blocks = []
        lines = section.content.split('\n')
        
        current_block = {'type': 'text', 'content': [], 'order': 0}
        block_order = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # Check for images
            if self.image_pattern.search(line):
                # Save current text block if it has content
                if current_block['content']:
                    current_block['content'] = '\n'.join(current_block['content'])
                    content_blocks.append(current_block)
                    block_order += 1
                
                # Create image block
                img_match = self.image_pattern.search(line)
                content_blocks.append({
                    'type': 'image',
                    'content': line,
                    'order': block_order,
                    'meta_data': {
                        'alt_text': img_match.group(1),
                        'src': img_match.group(2)
                    }
                })
                block_order += 1
                
                # Start new text block
                current_block = {'type': 'text', 'content': [], 'order': block_order}
            
            # Check for equations (LaTeX)
            elif '$' in line:
                # Save current text block if it has content
                if current_block['content']:
                    current_block['content'] = '\n'.join(current_block['content'])
                    content_blocks.append(current_block)
                    block_order += 1
                
                # Create equation block
                content_blocks.append({
                    'type': 'equation',
                    'content': line,
                    'order': block_order,
                    'search_text': re.sub(r'\$[^$]*\$', '[EQUATION]', line)
                })
                block_order += 1
                
                # Start new text block
                current_block = {'type': 'text', 'content': [], 'order': block_order}
            
            # Check for tables
            elif '|' in line:
                # Save current text block if it has content
                if current_block['content']:
                    current_block['content'] = '\n'.join(current_block['content'])
                    content_blocks.append(current_block)
                    block_order += 1
                
                # Create table block
                content_blocks.append({
                    'type': 'table',
                    'content': line,
                    'order': block_order,
                    'search_text': line.replace('|', ' ')
                })
                block_order += 1
                
                # Start new text block
                current_block = {'type': 'text', 'content': [], 'order': block_order}
            
            else:
                # Add to current text block
                current_block['content'].append(line)
        
        # Save final text block
        if current_block['content']:
            current_block['content'] = '\n'.join(current_block['content'])
            content_blocks.append(current_block)
        
        return content_blocks
    
    def generate_search_text(self, content: str) -> str:
        """Generate searchable text from markdown content"""
        # Remove markdown formatting
        text = re.sub(r'[#*_`]', '', content)
        # Remove image references
        text = re.sub(r'!\[[^\]]*\]\([^)]+\)', '', text)
        # Simplify equations for search
        text = re.sub(r'\$[^$]*\$', '[EQUATION]', text)
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        return text
