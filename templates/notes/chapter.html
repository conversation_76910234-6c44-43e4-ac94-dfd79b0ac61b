{% extends "base.html" %}

{% block title %}{{ chapter.title }} - Chemistry Notes - VAST{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <nav class="flex items-center space-x-2 text-sm text-gray-500 mb-2">
                        <a href="{{ url_for('notes_index') }}" class="hover:text-blue-600">
                            <i class="fas fa-book-open mr-1"></i>Chemistry Notes
                        </a>
                        <i class="fas fa-chevron-right text-xs"></i>
                        <span class="text-gray-900">{{ chapter.title }}</span>
                    </nav>
                    <h1 class="text-3xl font-bold text-gray-900">
                        {% if chapter.chapter_number %}
                        <span class="text-blue-600">{{ chapter.chapter_number }}.</span>
                        {% endif %}
                        {{ chapter.title }}
                    </h1>
                </div>
                <div class="flex items-center space-x-3">
                    <!-- Chapter Search -->
                    <div class="relative">
                        <input type="text" 
                               id="chapter-search" 
                               placeholder="Search in this chapter..."
                               class="w-64 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-10">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    </div>
                    <!-- Table of Contents Toggle -->
                    <button id="toc-toggle" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-list mr-2"></i>Contents
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex gap-8">
            <!-- Table of Contents Sidebar -->
            <div id="toc-sidebar" class="w-80 flex-shrink-0">
                <div class="bg-white rounded-lg shadow-sm border sticky top-8">
                    <div class="p-4 border-b border-gray-200">
                        <h3 class="font-semibold text-gray-900">Table of Contents</h3>
                    </div>
                    <div class="p-4 max-h-[70vh] overflow-y-auto">
                        <nav class="space-y-1">
                            {% for item in section_hierarchy %}
                                {% include 'notes/_toc_item.html' %}
                            {% endfor %}
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="flex-1 min-w-0">
                <div class="bg-white rounded-lg shadow-sm border">
                    <div class="p-8">
                        <!-- Chapter Description -->
                        {% if chapter.description %}
                        <div class="mb-8 p-4 bg-blue-50 border-l-4 border-blue-400 rounded-r-lg">
                            <p class="text-blue-800">{{ chapter.description }}</p>
                        </div>
                        {% endif %}

                        <!-- Sections Content -->
                        <div class="prose prose-lg max-w-none">
                            {% for section in sections %}
                            <div id="{{ section.hierarchical_anchor_id or section.section_id }}" class="section-content mb-8 scroll-mt-8">
                                <!-- Section Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <h{{ section.heading_level }} class="text-gray-900 font-bold mb-0">
                                        {{ section.title }}
                                    </h{{ section.heading_level }}>
                                    <div class="flex items-center space-x-2">
                                        {% if section.page_number %}
                                        <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                            Page {{ section.page_number }}
                                        </span>
                                        {% endif %}
                                        <button onclick="copyLink('{{ section.hierarchical_anchor_id or section.section_id }}')"
                                                class="text-gray-400 hover:text-blue-600 transition-colors"
                                                title="Copy link to this section">
                                            <i class="fas fa-link text-sm"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Section Content -->
                                {% if section.content %}
                                <div class="section-body">
                                    {{ section.content | markdown | safe }}
                                </div>
                                {% endif %}

                                <!-- Content Blocks -->
                                {% for block in section.content_blocks %}
                                <div class="content-block mb-4">
                                    {% if block.content_type == 'text' %}
                                        {{ block.content_data | markdown | safe }}
                                    {% elif block.content_type == 'image' %}
                                        <div class="text-center my-6">
                                            <img src="{{ url_for('serve_chemistry_notes_image', filename=block.meta_data.src) }}"
                                                 alt="{{ block.meta_data.alt_text }}"
                                                 class="max-w-full h-auto rounded-lg shadow-sm">
                                            {% if block.meta_data.alt_text %}
                                            <p class="text-sm text-gray-600 mt-2">{{ block.meta_data.alt_text }}</p>
                                            {% endif %}
                                        </div>
                                    {% elif block.content_type == 'equation' %}
                                        <div class="equation-block my-4 text-center">
                                            {{ block.content_data | safe }}
                                        </div>
                                    {% elif block.content_type == 'table' %}
                                        <div class="table-block my-4 overflow-x-auto">
                                            {{ block.content_data | markdown | safe }}
                                        </div>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Navigation Footer -->
                <div class="mt-8 flex justify-between items-center">
                    <a href="{{ url_for('notes_index') }}" 
                       class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Notes
                    </a>
                    
                    <div class="text-sm text-gray-500">
                        {{ sections|length }} sections • Last updated {{ chapter.updated_at.strftime('%B %d, %Y') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results Modal -->
<div id="search-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Search Results</h3>
                    <button onclick="closeSearchModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="modal-search-results" class="p-6 overflow-y-auto max-h-[60vh]">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>

<script>
let searchTimeout;

// Chapter search functionality
document.getElementById('chapter-search').addEventListener('input', function(e) {
    clearTimeout(searchTimeout);
    const query = e.target.value.trim();
    
    if (query.length < 2) {
        return;
    }
    
    searchTimeout = setTimeout(() => {
        performChapterSearch(query);
    }, 300);
});

function performChapterSearch(query) {
    const searchUrl = `/api/notes/search?q=${encodeURIComponent(query)}&chapter={{ chapter.slug }}`;
    
    fetch(searchUrl)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results, query);
        })
        .catch(error => {
            console.error('Search error:', error);
        });
}

function displaySearchResults(results, query) {
    const modalContainer = document.getElementById('modal-search-results');
    
    if (results.length === 0) {
        modalContainer.innerHTML = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-search text-3xl mb-2"></i>
                <p>No results found for "${query}" in this chapter</p>
            </div>
        `;
    } else {
        const resultsHtml = results.map(result => `
            <div class="border border-gray-200 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow">
                <h4 class="font-semibold text-gray-900 mb-1">
                    <a href="#${result.section_id}" onclick="closeSearchModal(); scrollToSection('${result.section_id}')" 
                       class="hover:text-blue-600">
                        ${result.title}
                    </a>
                </h4>
                ${result.page_number ? `<p class="text-sm text-gray-600 mb-2">Page ${result.page_number}</p>` : ''}
                <p class="text-sm text-gray-700">${result.snippet}</p>
            </div>
        `).join('');
        
        modalContainer.innerHTML = resultsHtml;
    }
    
    document.getElementById('search-modal').classList.remove('hidden');
}

function closeSearchModal() {
    document.getElementById('search-modal').classList.add('hidden');
}

function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        // Highlight the section briefly
        element.classList.add('bg-yellow-100');
        setTimeout(() => {
            element.classList.remove('bg-yellow-100');
        }, 2000);
    }
}

function copyLink(sectionId) {
    const url = `${window.location.origin}{{ url_for('view_section', chapter_slug=chapter.slug, section_id='SECTION_ID') }}`.replace('SECTION_ID', sectionId);
    navigator.clipboard.writeText(url).then(() => {
        // Show a brief success message
        const button = event.target.closest('button');
        const icon = button.querySelector('i');
        icon.className = 'fas fa-check text-sm text-green-600';
        setTimeout(() => {
            icon.className = 'fas fa-link text-sm';
        }, 2000);
    });
}

// Table of Contents toggle
document.getElementById('toc-toggle').addEventListener('click', function() {
    const sidebar = document.getElementById('toc-sidebar');
    sidebar.classList.toggle('hidden');
});

// Close modal when clicking outside
document.getElementById('search-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSearchModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeSearchModal();
    }
});

// Smooth scrolling for TOC links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    });
});

// Render LaTeX in the chemistry notes content
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for content to be fully loaded
    setTimeout(function() {
        if (typeof renderMathInElement !== 'undefined') {
            console.log('Rendering LaTeX in chemistry notes...');
            renderMathInElement(document.body, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html',
                trust: true
            });
            console.log('LaTeX rendering completed for chemistry notes');
        } else {
            console.warn('KaTeX auto-render not available');
        }
    }, 100);
});
</script>

<style>
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    scroll-margin-top: 2rem;
}

.section-content {
    border-left: 3px solid transparent;
    padding-left: 1rem;
    transition: all 0.3s ease;
}

.section-content:hover {
    border-left-color: #3b82f6;
    background-color: #f8fafc;
}

/* Enhanced table styling for chemistry content */
table {
    font-size: 0.9rem;
    line-height: 1.4;
}

table th {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    min-width: 120px;
}

table td {
    text-align: center;
    vertical-align: middle;
    min-height: 40px;
    position: relative;
}

/* Special styling for chemical formulas in tables */
table .katex {
    font-size: 0.9em;
}

table .katex-display {
    margin: 0.2em 0;
}

/* Responsive table wrapper */
.table-wrapper {
    overflow-x: auto;
    margin: 1.5rem 0;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Alternating row colors for better readability */
table tbody tr:nth-child(even) {
    background-color: #f8fafc;
}

table tbody tr:hover {
    background-color: #e2e8f0;
    transition: background-color 0.2s ease;
}

@media (max-width: 1024px) {
    #toc-sidebar {
        display: none;
    }

    #toc-sidebar.show {
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 40;
        background: rgba(0, 0, 0, 0.5);
    }

    /* Make tables more mobile-friendly */
    table {
        font-size: 0.8rem;
    }

    table th, table td {
        padding: 8px 4px;
        min-width: 80px;
    }
}
</style>
{% endblock %}
