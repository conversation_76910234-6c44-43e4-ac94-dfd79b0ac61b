{% extends "base.html" %}

{% block title %}Chemistry Notes - VAST{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                <i class="fas fa-book-open text-blue-600 mr-3"></i>
                Chemistry Notes
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Comprehensive H2 Chemistry lecture notes with searchable sections and hyperlinked content.
                Perfect for quick reference and deep study.
            </p>
        </div>

        <!-- Search Bar -->
        <div class="max-w-2xl mx-auto mb-12">
            <div class="relative">
                <input type="text" 
                       id="global-search" 
                       placeholder="Search across all chemistry notes..."
                       class="w-full px-6 py-4 text-lg border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 pl-12">
                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
            <div id="search-results" class="mt-4 hidden">
                <!-- Search results will be populated here -->
            </div>
        </div>

        <!-- Chapters Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for chapter in chapters %}
            <div class="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="p-6">
                    <!-- Chapter Number Badge -->
                    {% if chapter.chapter_number %}
                    <div class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-4">
                        Chapter {{ chapter.chapter_number }}
                    </div>
                    {% endif %}
                    
                    <!-- Chapter Title -->
                    <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2">
                        {{ chapter.title }}
                    </h3>
                    
                    <!-- Chapter Description -->
                    {% if chapter.description %}
                    <p class="text-gray-600 mb-4 line-clamp-3">
                        {{ chapter.description }}
                    </p>
                    {% endif %}
                    
                    <!-- Chapter Stats -->
                    <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                        <span>
                            <i class="fas fa-list-ul mr-1"></i>
                            {{ chapter.sections|length }} sections
                        </span>
                        <span>
                            <i class="fas fa-clock mr-1"></i>
                            Updated {{ chapter.updated_at.strftime('%b %d') }}
                        </span>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="flex space-x-3">
                        <a href="{{ url_for('view_chapter', chapter_slug=chapter.slug) }}" 
                           class="flex-1 bg-blue-600 hover:bg-blue-700 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors duration-200">
                            <i class="fas fa-book-reader mr-2"></i>
                            Read Notes
                        </a>
                        <button onclick="searchInChapter('{{ chapter.slug }}')" 
                                class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not chapters %}
        <div class="text-center py-16">
            <i class="fas fa-book text-6xl text-gray-300 mb-4"></i>
            <h3 class="text-xl font-medium text-gray-500 mb-2">No chemistry notes available</h3>
            <p class="text-gray-400">Notes will appear here once they are imported into the system.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Search Results Modal -->
<div id="search-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl max-w-4xl w-full max-h-[80vh] overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Search Results</h3>
                    <button onclick="closeSearchModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div id="modal-search-results" class="p-6 overflow-y-auto max-h-[60vh]">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>

<script>
let searchTimeout;
let currentChapterFilter = '';

document.getElementById('global-search').addEventListener('input', function(e) {
    clearTimeout(searchTimeout);
    const query = e.target.value.trim();
    
    if (query.length < 2) {
        hideSearchResults();
        return;
    }
    
    searchTimeout = setTimeout(() => {
        performSearch(query);
    }, 300);
});

function performSearch(query) {
    const searchUrl = `/api/notes/search?q=${encodeURIComponent(query)}${currentChapterFilter ? `&chapter=${currentChapterFilter}` : ''}`;
    
    fetch(searchUrl)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data.results, query);
        })
        .catch(error => {
            console.error('Search error:', error);
        });
}

function displaySearchResults(results, query) {
    const resultsContainer = document.getElementById('search-results');
    const modalContainer = document.getElementById('modal-search-results');
    
    if (results.length === 0) {
        const noResults = `
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-search text-3xl mb-2"></i>
                <p>No results found for "${query}"</p>
            </div>
        `;
        resultsContainer.innerHTML = noResults;
        modalContainer.innerHTML = noResults;
    } else {
        const resultsHtml = results.map(result => `
            <div class="bg-white border border-gray-200 rounded-lg p-4 mb-3 hover:shadow-md transition-shadow">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900 mb-1">
                            <a href="${result.url}" class="hover:text-blue-600">
                                ${result.title}
                            </a>
                        </h4>
                        <p class="text-sm text-gray-600 mb-2">
                            ${result.chapter_title} 
                            ${result.page_number ? `• Page ${result.page_number}` : ''}
                        </p>
                        <p class="text-sm text-gray-700">${result.snippet}</p>
                    </div>
                    <span class="text-xs text-gray-500 ml-4">h${result.heading_level}</span>
                </div>
            </div>
        `).join('');
        
        resultsContainer.innerHTML = resultsHtml;
        modalContainer.innerHTML = resultsHtml;
    }
    
    resultsContainer.classList.remove('hidden');
    document.getElementById('search-modal').classList.remove('hidden');

    // Render LaTeX in the search results
    setTimeout(renderLatexInSearchResults, 100);
}

function hideSearchResults() {
    document.getElementById('search-results').classList.add('hidden');
}

function searchInChapter(chapterSlug) {
    currentChapterFilter = chapterSlug;
    const searchInput = document.getElementById('global-search');
    searchInput.placeholder = `Search in chapter...`;
    searchInput.focus();
}

function closeSearchModal() {
    document.getElementById('search-modal').classList.add('hidden');
    currentChapterFilter = '';
    document.getElementById('global-search').placeholder = 'Search across all chemistry notes...';
}

// Close modal when clicking outside
document.getElementById('search-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSearchModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeSearchModal();
    }
});

// Render LaTeX in search results
function renderLatexInSearchResults() {
    if (typeof renderMathInElement !== 'undefined') {
        const searchResults = document.getElementById('modal-search-results');
        if (searchResults) {
            renderMathInElement(searchResults, {
                delimiters: [
                    {left: '$$', right: '$$', display: true},
                    {left: '$', right: '$', display: false},
                    {left: '\\(', right: '\\)', display: false},
                    {left: '\\[', right: '\\]', display: true}
                ],
                throwOnError: false,
                output: 'html'
            });
        }
    }
}
</script>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
{% endblock %}
