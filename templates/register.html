{% extends "base.html" %}

{% block content %}
<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <!-- Header -->
        <div class="text-center">
            <h1 class="text-4xl font-bold tracking-tight text-gray-900">Join Vast</h1>
            <p class="mt-2 text-sm text-gray-600">
                Create your account and start your learning journey
            </p>
        </div>

        <!-- Registration Form -->
        <div class="mt-8 bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10 transform transition-all hover:scale-[1.01] duration-300">
            {% if otp_sent %}
                <!-- OTP Verification Form -->
                <div class="text-center space-y-4 mb-6">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100">
                        <i class="fas fa-key text-blue-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">Verify Your Email</h3>
                    <p class="text-sm text-gray-600">
                        We've sent a verification code to <strong>{{ email }}</strong>.
                        Please enter the code to complete your registration.
                    </p>
                </div>

                <form method="POST" action="/register" class="space-y-6">
                    <input type="hidden" name="email" value="{{ email }}">

                    <div>
                        <label for="otp" class="block text-sm font-medium leading-6 text-gray-900">
                            Verification Code
                        </label>
                        <div class="mt-2 relative rounded-md shadow-sm">
                            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                            <input type="text" id="otp" name="otp" required maxlength="6"
                                   class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200 text-center text-lg tracking-widest"
                                   placeholder="000000"
                                   inputmode="numeric"
                                   pattern="[0-9]{6}">
                        </div>
                        <p class="mt-2 text-xs text-gray-500">
                            <i class="fas fa-info-circle mr-1"></i>
                            Enter the 6-digit code from your email.
                        </p>
                    </div>

                    <div>
                        <button type="submit"
                                class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-check text-indigo-300 group-hover:text-indigo-200"></i>
                            </span>
                            Verify & Create Account
                        </button>
                    </div>

                    <div class="text-center text-sm">
                        <a href="{{ url_for('register') }}" class="text-gray-600 hover:text-indigo-600 transition-colors duration-200">
                            ← Back to email entry
                        </a>
                    </div>
                </form>
            {% else %}
                <!-- Email Entry Form -->
                <form method="POST" action="/register" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium leading-6 text-gray-900">
                            Email Address
                        </label>
                        <div class="mt-2 relative rounded-md shadow-sm">
                            <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input type="email" id="email" name="email" required
                                   class="block w-full rounded-md border-0 py-2 pl-10 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 transition-colors duration-200"
                                   placeholder="Enter your email address">
                        </div>
                        <p class="mt-2 text-xs text-gray-500">
                            <i class="fas fa-shield-alt mr-1"></i>
                            We'll send you a verification code to confirm your email address.
                        </p>
                    </div>

                    <div>
                        <button type="submit"
                                class="group relative flex w-full justify-center rounded-md bg-indigo-600 px-3 py-3 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 transition-all duration-200">
                            <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                <i class="fas fa-paper-plane text-indigo-300 group-hover:text-indigo-200"></i>
                            </span>
                            Send Verification Code
                        </button>
                    </div>

                    <div class="text-center text-sm">
                        <p class="text-gray-600">
                            Already have an account?
                            <a href="{{ url_for('login') }}" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                                Sign in
                            </a>
                        </p>
                    </div>
                </form>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}