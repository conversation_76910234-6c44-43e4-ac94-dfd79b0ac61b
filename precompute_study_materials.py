#!/usr/bin/env python3
"""
Precompute study materials for every requirement by querying Pinecone
and saving the results to the study_materials database table
"""

import sys
import os
import json
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from app import create_app
from models import db, Part
from preprocess import PlatoPreprocess

def precompute_all_study_materials(max_workers=4, subject_id=None, test_mode=False):
    """
    Precompute study materials for all requirements by querying Pinecone
    
    Args:
        max_workers: Number of parallel workers
        subject_id: Optional subject filter
        test_mode: If True, only process first 5 parts
    """
    
    app = create_app()
    
    with app.app_context():
        print("🔍 Querying database for parts with content rubrics...")

        # Get all parts with content rubrics
        query = Part.query.filter(Part.content_rubric.isnot(None))

        if subject_id:
            print(f"🎯 Filtering by subject ID: {subject_id}")
            from models import Question, Topic
            query = query.join(Question).join(Topic).filter(Topic.subject_id == subject_id)

        parts = query.all()

        if test_mode:
            parts = parts[:5]
            print(f"🧪 Test mode: Processing only {len(parts)} parts")

        print(f"Found {len(parts)} parts with content rubrics")
        
        if not parts:
            print("❌ No parts found with content rubrics")
            return
        
        # Initialize processor
        print("🔧 Initializing Pinecone processor...")
        processor = PlatoPreprocess()
        print("✅ Processor initialized")
        
        # Count total requirements
        total_requirements = 0
        for part in parts:
            try:
                content_rubric = json.loads(part.content_rubric)
                total_requirements += len(content_rubric)
            except:
                continue
        
        print(f"📊 Total requirements to process: {total_requirements}")
        
        # Process parts in parallel
        start_time = time.time()
        processed_count = 0
        success_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all parts for processing
            future_to_part = {
                executor.submit(process_part_requirements, part, processor): part 
                for part in parts
            }
            
            # Process results as they complete
            for future in as_completed(future_to_part):
                part = future_to_part[future]
                processed_count += 1
                
                try:
                    result = future.result()
                    if result['success']:
                        success_count += result['requirements_processed']
                        print(f"[{processed_count}/{len(parts)}] ✅ Part {part.id}: {result['requirements_processed']} requirements processed")
                    else:
                        print(f"[{processed_count}/{len(parts)}] ❌ Part {part.id}: {result['error']}")
                        
                except Exception as e:
                    print(f"[{processed_count}/{len(parts)}] ❌ Part {part.id}: Exception - {str(e)}")
        
        # Summary
        elapsed_time = time.time() - start_time
        print(f"\n🎉 Precomputing completed!")
        print(f"✅ Successfully processed: {success_count}/{total_requirements} requirements")
        print(f"⏱️  Total time: {elapsed_time:.2f} seconds")
        print(f"📈 Average time per requirement: {elapsed_time/total_requirements:.2f} seconds")

def process_part_requirements(part, processor):
    """
    Process all requirements for a single part

    Args:
        part: Part object
        processor: PlatoPreprocess instance

    Returns:
        dict: Result summary
    """
    # Create new app context for this worker
    app = create_app()

    with app.app_context():
        try:
            # Parse content rubric
            content_rubric = json.loads(part.content_rubric)

            requirements_processed = 0

            for req_id, requirement_text in content_rubric.items():
                try:
                    # Create search query combining requirement and part description
                    search_query = f"{requirement_text} {part.description}"

                    # Query Pinecone
                    pinecone_results = processor.search_pinecone_for_materials(search_query, top_k=3)

                    # Save to database
                    save_study_material(part.id, req_id, requirement_text, part.description, pinecone_results)

                    requirements_processed += 1

                except Exception as e:
                    print(f"   ❌ Error processing requirement {req_id}: {str(e)}")
                    continue

            return {
                'success': True,
                'requirements_processed': requirements_processed
            }

        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'requirements_processed': 0
            }

def save_study_material(part_id, requirement_id, requirement_text, part_description, pinecone_results):
    """
    Save study material to database
    
    Args:
        part_id: Part ID
        requirement_id: Requirement ID
        requirement_text: Requirement text
        part_description: Part description
        pinecone_results: List of Pinecone results
    """
    try:
        # Convert results to JSON payload
        payload = json.dumps(pinecone_results)
        
        # Insert or update in database
        with db.engine.connect() as conn:
            # Check if record exists
            result = conn.execute(db.text("""
                SELECT id FROM study_materials 
                WHERE part_id = :part_id AND requirement_id = :requirement_id
            """), {
                'part_id': part_id,
                'requirement_id': requirement_id
            })
            
            existing = result.fetchone()
            
            if existing:
                # Update existing record
                conn.execute(db.text("""
                    UPDATE study_materials 
                    SET requirement_text = :requirement_text,
                        part_description = :part_description,
                        pinecone_payload = :payload,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE part_id = :part_id AND requirement_id = :requirement_id
                """), {
                    'part_id': part_id,
                    'requirement_id': requirement_id,
                    'requirement_text': requirement_text,
                    'part_description': part_description,
                    'payload': payload
                })
            else:
                # Insert new record
                conn.execute(db.text("""
                    INSERT INTO study_materials 
                    (part_id, requirement_id, requirement_text, part_description, pinecone_payload)
                    VALUES (:part_id, :requirement_id, :requirement_text, :part_description, :payload)
                """), {
                    'part_id': part_id,
                    'requirement_id': requirement_id,
                    'requirement_text': requirement_text,
                    'part_description': part_description,
                    'payload': payload
                })
            
            conn.commit()
            
    except Exception as e:
        print(f"   ❌ Database error for part {part_id}, req {requirement_id}: {str(e)}")
        raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='Precompute study materials from Pinecone')
    parser.add_argument('--workers', type=int, default=4, help='Number of parallel workers')
    parser.add_argument('--subject-id', type=int, help='Filter by subject ID')
    parser.add_argument('--test', action='store_true', help='Test mode (process only 5 parts)')
    
    args = parser.parse_args()
    
    precompute_all_study_materials(
        max_workers=args.workers,
        subject_id=args.subject_id,
        test_mode=args.test
    )
