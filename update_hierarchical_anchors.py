#!/usr/bin/env python3
"""
Update existing database with hierarchical anchor IDs
"""

import os
import sys
import re
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import db, ChemistryChapter, ChapterSection
from config import Config
from upsert_pinecone import ChemistryNotesExtractor

def generate_anchor_id(heading_text: str, chapter_slug: str, counter: int) -> str:
    """Generate a clean anchor ID for the heading"""
    # Clean the heading text
    clean_text = re.sub(r'[^\w\s-]', '', heading_text.lower())
    clean_text = re.sub(r'\s+', '-', clean_text.strip())
    clean_text = re.sub(r'-+', '-', clean_text)
    clean_text = clean_text.strip('-')
    
    # Limit length and add counter for uniqueness
    if len(clean_text) > 50:
        clean_text = clean_text[:50].rstrip('-')
    
    return f"{chapter_slug}-{clean_text}-{counter}"

def update_database_with_anchors():
    """Update existing database sections with hierarchical anchor IDs"""
    print("🔄 Updating database with hierarchical anchor IDs...")
    
    # Setup database
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # First, add the new column if it doesn't exist
        try:
            session.execute(text("ALTER TABLE chapter_section ADD COLUMN hierarchical_anchor_id VARCHAR(200)"))
            session.commit()
            print("✅ Added hierarchical_anchor_id column to database")
        except Exception as e:
            if "duplicate column name" in str(e).lower() or "already exists" in str(e).lower():
                print("ℹ️  Column hierarchical_anchor_id already exists")
            else:
                print(f"⚠️  Error adding column: {e}")
        
        # Get all chapters
        chapters = session.query(ChemistryChapter).filter_by(is_active=True).all()
        print(f"📚 Found {len(chapters)} chapters to process")
        
        total_updated = 0
        
        for chapter in chapters:
            print(f"📖 Processing chapter: {chapter.title}")
            
            # Get all sections for this chapter
            sections = session.query(ChapterSection).filter_by(
                chapter_id=chapter.id
            ).order_by(ChapterSection.order_index).all()
            
            # Generate anchor IDs for each section
            counter = 1
            for section in sections:
                # Skip if already has hierarchical anchor
                if section.hierarchical_anchor_id:
                    continue
                
                # Generate new anchor ID
                anchor_id = generate_anchor_id(section.title, chapter.slug, counter)
                section.hierarchical_anchor_id = anchor_id
                counter += 1
                total_updated += 1
            
            print(f"   ✅ Updated {len(sections)} sections")
        
        # Commit all changes
        session.commit()
        print(f"🎉 Successfully updated {total_updated} sections with hierarchical anchor IDs")
        
    except Exception as e:
        session.rollback()
        print(f"❌ Error updating database: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    finally:
        session.close()

def verify_anchors():
    """Verify that anchor IDs were added correctly"""
    print("\n🔍 Verifying anchor ID updates...")
    
    engine = create_engine(Config.SQLALCHEMY_DATABASE_URI)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Count sections with and without hierarchical anchors
        total_sections = session.query(ChapterSection).count()
        sections_with_anchors = session.query(ChapterSection).filter(
            ChapterSection.hierarchical_anchor_id.isnot(None)
        ).count()
        
        print(f"📊 Verification Results:")
        print(f"   Total sections: {total_sections}")
        print(f"   Sections with hierarchical anchors: {sections_with_anchors}")
        print(f"   Coverage: {(sections_with_anchors/total_sections*100):.1f}%")
        
        # Show some examples
        sample_sections = session.query(ChapterSection).filter(
            ChapterSection.hierarchical_anchor_id.isnot(None)
        ).limit(5).all()
        
        print(f"\n📝 Sample anchor IDs:")
        for section in sample_sections:
            print(f"   '{section.title}' → '{section.hierarchical_anchor_id}'")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
    
    finally:
        session.close()

if __name__ == "__main__":
    update_database_with_anchors()
    verify_anchors()
